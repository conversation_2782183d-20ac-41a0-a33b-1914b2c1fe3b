<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CARLIN/DARLIN Biological Sequences and Editing Mechanisms</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #9b59b6;
            --dark-color: #34495e;
            --light-bg: #ecf0f1;
            --text-color: #2c3e50;
            --border-color: #bdc3c7;
            --dna-a: #ff6b6b;
            --dna-t: #4ecdc4;
            --dna-g: #45b7d1;
            --dna-c: #96ceb4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 25px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            text-shadow: 3px 3px 8px rgba(0,0,0,0.4);
        }

        .header p {
            font-size: 1.6em;
            opacity: 0.9;
        }

        .outline {
            background: white;
            padding: 50px;
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            margin-bottom: 30px;
            border-left: 12px solid var(--info-color);
        }

        .outline h2 {
            color: var(--primary-color);
            margin-bottom: 35px;
            font-size: 2.6em;
        }

        .outline ol {
            padding-left: 40px;
        }

        .outline li {
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .outline li ul {
            margin-top: 15px;
            padding-left: 40px;
        }

        .outline li ul li {
            font-size: 1.15em;
            color: #666;
            margin-bottom: 12px;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-left: 12px solid var(--accent-color);
        }

        .section-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
            padding: 40px 50px;
            font-size: 2.2em;
            font-weight: bold;
        }

        .section-content {
            padding: 50px;
        }

        .sequence-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 3px solid var(--success-color);
            padding: 35px;
            margin: 35px 0;
            border-radius: 15px;
            font-family: 'Courier New', monospace;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .dna-sequence {
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            letter-spacing: 2px;
            line-height: 2;
            word-break: break-all;
            padding: 25px;
            background: #2c3e50;
            color: white;
            border-radius: 10px;
            margin: 20px 0;
        }

        .nucleotide-a { color: var(--dna-a); font-weight: bold; }
        .nucleotide-t { color: var(--dna-t); font-weight: bold; }
        .nucleotide-g { color: var(--dna-g); font-weight: bold; }
        .nucleotide-c { color: var(--dna-c); font-weight: bold; }

        .editing-example {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 3px solid var(--warning-color);
            padding: 35px;
            margin: 35px 0;
            border-radius: 15px;
            border-left: 10px solid var(--warning-color);
        }

        .mutation-table {
            width: 100%;
            border-collapse: collapse;
            margin: 35px 0;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .mutation-table th {
            background: var(--primary-color);
            color: white;
            padding: 25px;
            text-align: left;
            font-size: 1.3em;
        }

        .mutation-table td {
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-color);
            font-size: 1.1em;
        }

        .mutation-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .svg-container {
            text-align: center;
            margin: 45px 0;
            padding: 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .key-insight {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left: 10px solid var(--success-color);
            padding: 35px;
            margin: 35px 0;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .mechanism-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 10px solid var(--secondary-color);
            padding: 35px;
            margin: 30px 0;
            border-radius: 15px;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 35px;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            max-width: 300px;
            z-index: 1000;
        }

        .navigation h3 {
            color: var(--primary-color);
            margin-bottom: 25px;
            font-size: 1.4em;
        }

        .navigation ul {
            list-style: none;
        }

        .navigation li {
            margin-bottom: 15px;
        }

        .navigation a {
            color: var(--secondary-color);
            text-decoration: none;
            font-size: 1.1em;
            display: block;
            padding: 15px;
            border-radius: 12px;
            transition: all 0.3s;
        }

        .navigation a:hover {
            background: var(--light-bg);
            transform: translateX(10px);
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2.8em;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <h3>Sequence Guide</h3>
        <ul>
            <li><a href="#overview">CARLIN Overview</a></li>
            <li><a href="#sequences">Sequence Structure</a></li>
            <li><a href="#editing">Editing Mechanisms</a></li>
            <li><a href="#arrays">Three-Array System</a></li>
            <li><a href="#mutations">Mutation Patterns</a></li>
            <li><a href="#analysis">Output Analysis</a></li>
            <li><a href="#applications">Applications</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>CARLIN/DARLIN</h1>
            <p>Biological Sequences and Editing Mechanisms</p>
        </div>

        <div class="outline">
            <h2>🧬 Tutorial Outline</h2>
            <ol>
                <li><strong>CARLIN System Overview</strong>
                    <ul>
                        <li>CARLIN vs DARLIN terminology and evolution</li>
                        <li>Biological purpose and lineage tracing principles</li>
                        <li>Integration sites: Col1a1, Tigre, Rosa26 loci</li>
                        <li>Cas9-mediated editing and TdT-mediated insertions</li>
                    </ul>
                </li>
                <li><strong>Sequence Structure and Components</strong>
                    <ul>
                        <li>Three-array architecture (CA, TA, RA)</li>
                        <li>Primer sequences and amplification regions</li>
                        <li>Target sites and PAM sequences</li>
                        <li>Repetitive motif structure and design rationale</li>
                    </ul>
                </li>
                <li><strong>Editing Mechanisms and Cas9 Activity</strong>
                    <ul>
                        <li>Double-strand break induction</li>
                        <li>Non-homologous end joining (NHEJ) repair</li>
                        <li>TdT-mediated random insertions</li>
                        <li>Deletion patterns and size distributions</li>
                    </ul>
                </li>
                <li><strong>Three-Array System Architecture</strong>
                    <ul>
                        <li>CA array (Col1a1/cCARLIN locus)</li>
                        <li>TA array (Tigre locus)</li>
                        <li>RA array (Rosa26 locus)</li>
                        <li>Combinatorial barcode generation</li>
                    </ul>
                </li>
                <li><strong>Mutation Patterns and Classification</strong>
                    <ul>
                        <li>Deletion nomenclature (e.g., 23_268del)</li>
                        <li>Insertion patterns (e.g., 265_266insG)</li>
                        <li>Complex mutations (delins events)</li>
                        <li>Frequency distributions and power laws</li>
                    </ul>
                </li>
                <li><strong>Output Sequence Analysis</strong>
                    <ul>
                        <li>Allele annotation and frequency calculation</li>
                        <li>Editing efficiency measurements</li>
                        <li>Quality control and validation</li>
                        <li>Reference allele bank construction</li>
                    </ul>
                </li>
                <li><strong>Applications and Biological Insights</strong>
                    <ul>
                        <li>Lineage tracing in development</li>
                        <li>Clonal dynamics and migration patterns</li>
                        <li>Single-cell and bulk analysis integration</li>
                        <li>Temporal lineage reconstruction</li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 1: CARLIN System Overview -->
        <div class="section" id="overview">
            <div class="section-header">
                🧬 1. CARLIN System Overview
            </div>
            <div class="section-content">
                <h3>CARLIN vs DARLIN: Evolution of Lineage Tracing</h3>
                <p><strong>CARLIN</strong> (Cas9-Assisted Recording of Lineage Information) was the original single-array system, while <strong>DARLIN</strong> (Dual/Triple Array Recording of Lineage Information) represents the advanced three-array evolution for higher resolution lineage tracing.</p>

                <div class="svg-container">
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <rect width="800" height="400" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">CARLIN to DARLIN Evolution</text>

                        <!-- CARLIN (original) -->
                        <rect x="50" y="60" width="300" height="120" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="200" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">CARLIN (Original)</text>
                        <text x="200" y="105" text-anchor="middle" font-size="12" fill="#333">Single Array System</text>

                        <!-- Single array representation -->
                        <rect x="80" y="125" width="240" height="25" fill="#3498db" rx="5"/>
                        <text x="200" y="142" text-anchor="middle" font-size="10" fill="white">Single CARLIN Array</text>

                        <text x="200" y="165" text-anchor="middle" font-size="11" fill="#333">Limited barcode diversity</text>

                        <!-- DARLIN (advanced) -->
                        <rect x="450" y="60" width="300" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="12"/>
                        <text x="600" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">DARLIN (Advanced)</text>
                        <text x="600" y="105" text-anchor="middle" font-size="12" fill="#333">Three Array System</text>

                        <!-- Three arrays representation -->
                        <rect x="480" y="125" width="70" height="15" fill="#e74c3c" rx="3"/>
                        <text x="515" y="136" text-anchor="middle" font-size="8" fill="white">CA Array</text>

                        <rect x="560" y="125" width="70" height="15" fill="#f39c12" rx="3"/>
                        <text x="595" y="136" text-anchor="middle" font-size="8" fill="white">TA Array</text>

                        <rect x="640" y="125" width="70" height="15" fill="#9b59b6" rx="3"/>
                        <text x="675" y="136" text-anchor="middle" font-size="8" fill="white">RA Array</text>

                        <text x="600" y="165" text-anchor="middle" font-size="11" fill="#333">Exponentially increased diversity</text>

                        <!-- Evolution arrow -->
                        <path d="M 350 120 L 450 120" stroke="#666" stroke-width="4" marker-end="url(#arrowhead)"/>
                        <text x="400" y="110" text-anchor="middle" font-size="12" font-weight="bold" fill="#666">Evolution</text>

                        <!-- Biological context -->
                        <rect x="100" y="220" width="600" height="150" fill="white" stroke="#34495e" stroke-width="2" rx="12"/>
                        <text x="400" y="245" text-anchor="middle" font-size="16" font-weight="bold" fill="#34495e">Biological Integration Sites</text>

                        <!-- Integration sites -->
                        <rect x="130" y="270" width="160" height="80" fill="#e74c3c" rx="8"/>
                        <text x="210" y="290" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Col1a1 Locus</text>
                        <text x="210" y="305" text-anchor="middle" font-size="10" fill="white">CA Array (cCARLIN)</text>
                        <text x="210" y="320" text-anchor="middle" font-size="9" fill="white">Collagen gene</text>
                        <text x="210" y="335" text-anchor="middle" font-size="9" fill="white">Ubiquitous expression</text>

                        <rect x="320" y="270" width="160" height="80" fill="#f39c12" rx="8"/>
                        <text x="400" y="290" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Tigre Locus</text>
                        <text x="400" y="305" text-anchor="middle" font-size="10" fill="white">TA Array</text>
                        <text x="400" y="320" text-anchor="middle" font-size="9" fill="white">Safe harbor site</text>
                        <text x="400" y="335" text-anchor="middle" font-size="9" fill="white">Controlled expression</text>

                        <rect x="510" y="270" width="160" height="80" fill="#9b59b6" rx="8"/>
                        <text x="590" y="290" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Rosa26 Locus</text>
                        <text x="590" y="305" text-anchor="middle" font-size="10" fill="white">RA Array</text>
                        <text x="590" y="320" text-anchor="middle" font-size="9" fill="white">Safe harbor site</text>
                        <text x="590" y="335" text-anchor="middle" font-size="9" fill="white">Constitutive expression</text>

                        <defs>
                            <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Lineage Tracing Principle</h3>
                <p>CARLIN/DARLIN systems work by introducing <strong>irreversible, heritable DNA modifications</strong> that serve as molecular barcodes:</p>

                <div class="mechanism-box">
                    <strong>🎯 Core Mechanism:</strong>
                    <ol>
                        <li><strong>Induction:</strong> Doxycycline activates Cas9 expression</li>
                        <li><strong>Editing:</strong> Cas9 creates double-strand breaks at target sites</li>
                        <li><strong>Repair:</strong> NHEJ repair introduces random insertions/deletions</li>
                        <li><strong>Inheritance:</strong> Edited sequences are passed to daughter cells</li>
                        <li><strong>Detection:</strong> PCR amplification and sequencing reveals barcodes</li>
                    </ol>
                </div>

                <h3>Terminology Clarification</h3>
                <div class="key-insight">
                    <strong>📝 Historical Note:</strong> The original CARLIN system used CC, TC, RC nomenclature, which corresponds to CA, TA, RA in the current DARLIN system. These terms are equivalent:
                    <ul>
                        <li><strong>CC ↔ CA:</strong> Col1a1/cCARLIN array</li>
                        <li><strong>TC ↔ TA:</strong> Tigre array</li>
                        <li><strong>RC ↔ RA:</strong> Rosa26 array</li>
                    </ul>
                </div>

                <h3>Barcode Diversity Mathematics</h3>
                <p>The three-array system dramatically increases barcode diversity:</p>

                <div class="editing-example">
                    <h4>Theoretical Barcode Capacity</h4>
                    $$\text{Total Barcodes} = N_{CA} \times N_{TA} \times N_{RA}$$

                    <p>Where each array can generate thousands of unique alleles through Cas9 editing.</p>

                    <p><strong>Practical Diversity:</strong></p>
                    $$\text{Effective Barcodes} \approx 10^6 \text{ to } 10^8$$

                    <p>This provides sufficient resolution for tracing individual cell lineages in complex tissues and developmental processes.</p>
                </div>
            </div>
        </div>

        <!-- Section 2: Sequence Structure -->
        <div class="section" id="sequences">
            <div class="section-header">
                🔬 2. Sequence Structure and Components
            </div>
            <div class="section-content">
                <h3>CARLIN Array Architecture</h3>
                <p>Each CARLIN array consists of repetitive DNA motifs designed to be efficiently edited by Cas9:</p>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <rect width="800" height="500" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">CARLIN Array Structure</text>

                        <!-- Generic array structure -->
                        <rect x="50" y="60" width="700" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Generic CARLIN Array Structure</text>

                        <!-- 5' primer -->
                        <rect x="80" y="105" width="100" height="25" fill="#e74c3c" rx="5"/>
                        <text x="130" y="122" text-anchor="middle" font-size="10" fill="white">5' Primer</text>

                        <!-- Repetitive motifs -->
                        <rect x="200" y="105" width="80" height="25" fill="#27ae60" rx="5"/>
                        <text x="240" y="122" text-anchor="middle" font-size="9" fill="white">Motif 1</text>

                        <rect x="290" y="105" width="80" height="25" fill="#27ae60" rx="5"/>
                        <text x="330" y="122" text-anchor="middle" font-size="9" fill="white">Motif 2</text>

                        <rect x="380" y="105" width="80" height="25" fill="#27ae60" rx="5"/>
                        <text x="420" y="122" text-anchor="middle" font-size="9" fill="white">Motif 3</text>

                        <text x="480" y="122" text-anchor="middle" font-size="12" fill="#333">...</text>

                        <rect x="520" y="105" width="80" height="25" fill="#27ae60" rx="5"/>
                        <text x="560" y="122" text-anchor="middle" font-size="9" fill="white">Motif N</text>

                        <!-- 3' primer -->
                        <rect x="620" y="105" width="100" height="25" fill="#9b59b6" rx="5"/>
                        <text x="670" y="122" text-anchor="middle" font-size="10" fill="white">3' Primer</text>

                        <!-- Detailed CA array structure -->
                        <rect x="50" y="170" width="700" height="120" fill="#ffebee" stroke="#e74c3c" stroke-width="3" rx="12"/>
                        <text x="400" y="195" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">CA Array (Col1a1/cCARLIN) - Detailed Structure</text>

                        <!-- CA components -->
                        <rect x="80" y="220" width="140" height="60" fill="#e74c3c" rx="8"/>
                        <text x="150" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">5' Primer</text>
                        <text x="150" y="255" text-anchor="middle" font-size="9" fill="white">GAGCTGTACAAGTAAGCGGC</text>
                        <text x="150" y="270" text-anchor="middle" font-size="8" fill="white">(20 bp)</text>

                        <rect x="240" y="220" width="320" height="60" fill="#27ae60" rx="8"/>
                        <text x="400" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">CARLIN Repetitive Array</text>
                        <text x="400" y="255" text-anchor="middle" font-size="9" fill="white">10 repetitive motifs with Cas9 target sites</text>
                        <text x="400" y="270" text-anchor="middle" font-size="8" fill="white">(~300 bp)</text>

                        <rect x="580" y="220" width="140" height="60" fill="#9b59b6" rx="8"/>
                        <text x="650" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">3' Primer</text>
                        <text x="650" y="255" text-anchor="middle" font-size="9" fill="white">AGAATTCTAACTAGAGCTC...</text>
                        <text x="650" y="270" text-anchor="middle" font-size="8" fill="white">(44 bp)</text>

                        <!-- Motif detail -->
                        <rect x="100" y="320" width="600" height="100" fill="white" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="400" y="340" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Repetitive Motif Structure</text>

                        <text x="130" y="360" font-size="11" font-weight="bold" fill="#333">Each motif contains:</text>
                        <text x="150" y="375" font-size="10" fill="#333">• Cas9 target sequence (20 bp)</text>
                        <text x="150" y="390" font-size="10" fill="#333">• PAM sequence (NGG)</text>
                        <text x="150" y="405" font-size="10" fill="#333">• Spacer regions for editing</text>

                        <text x="450" y="360" font-size="11" font-weight="bold" fill="#333">Design features:</text>
                        <text x="470" y="375" font-size="10" fill="#333">• Multiple cut sites per array</text>
                        <text x="470" y="390" font-size="10" fill="#333">• Optimized for NHEJ repair</text>
                        <text x="470" y="405" font-size="10" fill="#333">• TdT insertion hotspots</text>

                        <!-- Length information -->
                        <rect x="150" y="440" width="500" height="30" fill="#d4edda" stroke="#27ae60" rx="5"/>
                        <text x="400" y="460" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Total Array Length: ~364 bp (CA), ~320 bp (TA), ~318 bp (RA)</text>
                    </svg>
                </div>

                <h3>Actual Sequence Definitions</h3>
                <p>The three arrays have distinct sequences optimized for different genomic contexts:</p>

                <div class="sequence-box">
                    <h4>CA Array (Col1a1/cCARLIN) - 364 bp</h4>
                    <div class="dna-sequence" id="ca-sequence">
                        <!-- CA sequence will be colored by JavaScript -->
                        CGCCGGACTGCACGACAGTCGACGATGGAGTCGACACGACTCGCGCATACGATGGAGTCGACTACAGTCGCTACGACGATGGAGTCGCGAGCGCTATGAGCGACTATGGAGTCGATACGATACGCGCACGCTATGGAGTCGAGAGCGCGCTCGTCGACTATGGAGTCGCGACTGTACGCACACGCGATGGAGTCGATAGTATGCGTACACGCGATGGAGTCGAGTCGAGACGCTGACGATATGGAGTCGATACGTAGCACGCAGACGATGGGAGCT
                    </div>
                </div>

                <div class="sequence-box">
                    <h4>TA Array (Tigre) - 320 bp</h4>
                    <div class="dna-sequence" id="ta-sequence">
                        TCGCCGGAGTCGAGACGCTGACGATATGGAGTCGACACGACTCGCGCATACGATGGAGTCGCGAGCGCTATGAGCGACTATGGAGTCGATAGTATGCGTACACGCGATGGAGTCGACTACAGTCGCTACGACGATGGAGTCGATACGATACGCGCACGCTATGGAGTCGCGACTGTACGCACACGCGATGGAGTCGACTGCACGACAGTCGACGATGGAGTCGATACGTAGCACGCAGACGATGGGAGCGAGAGCGCGCTCGTCGACTATGGA
                    </div>
                </div>

                <div class="sequence-box">
                    <h4>RA Array (Rosa26) - 318 bp</h4>
                    <div class="dna-sequence" id="ra-sequence">
                        GCGCCGGCGAGCGCTATGAGCGACTATGGAGTCGACACGACTCGCGCATACGATGGAGTCGACTACAGTCGCTACGACGATGGAGTCGATACGATACGCGCACGCTATGGAGTCGACTGCACGACAGTCGACGATGGAGTCGATACGTAGCACGCAGACGATGGGAGCGAGTCGAGACGCTGACGATATGGAGTCGATAGTATGCGTACACGCGATGGAGTCGCGACTGTACGCACACGCGATGGAGTCGAGAGCGCGCTCGTCGACTATGGA
                    </div>
                </div>

                <h3>Primer Sequences for Amplification</h3>
                <p>Each array has specific primer sequences for PCR amplification and sequencing:</p>

                <table class="mutation-table">
                    <thead>
                        <tr>
                            <th>Array</th>
                            <th>5' Primer (Full)</th>
                            <th>3' Primer (Bulk)</th>
                            <th>Length</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>CA (cCARLIN)</strong></td>
                            <td>GAGCTGTACAAGTAAGCGGC</td>
                            <td>AGAATTCTAACTAGAGCTCGCTGATCAGCCTCGACTGTGCCTTCT</td>
                            <td>20 + 44 bp</td>
                        </tr>
                        <tr>
                            <td><strong>TA (Tigre)</strong></td>
                            <td>GCTCGGTACCTCGCGAA</td>
                            <td>GTCTTGTCGGTGCCTTCTAGTT</td>
                            <td>17 + 22 bp</td>
                        </tr>
                        <tr>
                            <td><strong>RA (Rosa26)</strong></td>
                            <td>ATGTACAAGTAAAGCGGCC</td>
                            <td>GTCTGCTGTGTGCCTTCTAGTT</td>
                            <td>19 + 22 bp</td>
                        </tr>
                    </tbody>
                </table>

                <div class="key-insight">
                    <strong>🔑 Sequence Design Strategy:</strong> The repetitive motif structure provides multiple Cas9 target sites within each array, increasing the probability of editing events while maintaining sequence integrity for PCR amplification and analysis.
                </div>
            </div>
        </div>

        <script>
            // Color DNA sequences by nucleotide
            function colorDNASequence(elementId) {
                const element = document.getElementById(elementId);
                if (element) {
                    let sequence = element.textContent.trim();
                    let coloredSequence = '';
                    for (let i = 0; i < sequence.length; i++) {
                        const nucleotide = sequence[i];
                        switch(nucleotide) {
                            case 'A':
                                coloredSequence += `<span class="nucleotide-a">${nucleotide}</span>`;
                                break;
                            case 'T':
                                coloredSequence += `<span class="nucleotide-t">${nucleotide}</span>`;
                                break;
                            case 'G':
                                coloredSequence += `<span class="nucleotide-g">${nucleotide}</span>`;
                                break;
                            case 'C':
                                coloredSequence += `<span class="nucleotide-c">${nucleotide}</span>`;
                                break;
                            default:
                                coloredSequence += nucleotide;
                        }
                    }
                    element.innerHTML = coloredSequence;
                }
            }

            // Color all DNA sequences when page loads
            document.addEventListener('DOMContentLoaded', function() {
                colorDNASequence('ca-sequence');
                colorDNASequence('ta-sequence');
                colorDNASequence('ra-sequence');
            });
        </script>

        <!-- Section 3: Editing Mechanisms -->
        <div class="section" id="editing">
            <div class="section-header">
                ✂️ 3. Editing Mechanisms and Cas9 Activity
            </div>
            <div class="section-content">
                <h3>Cas9-Mediated Double-Strand Break Formation</h3>
                <p>The CARLIN system relies on Cas9 nuclease to create precise double-strand breaks at target sites within the repetitive arrays:</p>

                <div class="svg-container">
                    <svg width="800" height="450" viewBox="0 0 800 450">
                        <rect width="800" height="450" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Cas9 Editing Mechanism</text>

                        <!-- Step 1: Intact sequence -->
                        <rect x="50" y="60" width="700" height="60" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="400" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Step 1: Intact CARLIN Array</text>

                        <!-- Intact sequence representation -->
                        <rect x="150" y="95" width="500" height="20" fill="#27ae60" rx="3"/>
                        <text x="400" y="108" text-anchor="middle" font-size="9" fill="white">...CGACGATGGAGTCGACACGACTCGCGCATACGATGGAGTCGACTACAGTCG...</text>

                        <!-- Step 2: Cas9 binding -->
                        <rect x="50" y="140" width="700" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="400" y="160" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">Step 2: Cas9 Binding and Cleavage</text>

                        <!-- Cas9 complex -->
                        <ellipse cx="400" cy="185" rx="60" ry="25" fill="#f39c12"/>
                        <text x="400" y="192" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Cas9-gRNA</text>

                        <!-- Target sequence with PAM -->
                        <rect x="200" y="200" width="180" height="15" fill="#e74c3c" rx="2"/>
                        <text x="290" y="211" text-anchor="middle" font-size="8" fill="white">Target (20bp) + PAM (NGG)</text>

                        <rect x="420" y="200" width="180" height="15" fill="#27ae60" rx="2"/>
                        <text x="510" y="211" text-anchor="middle" font-size="8" fill="white">Remaining sequence</text>

                        <!-- Step 3: Double-strand break -->
                        <rect x="50" y="240" width="700" height="80" fill="#ffebee" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="400" y="260" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Step 3: Double-Strand Break Formation</text>

                        <!-- Broken sequences -->
                        <rect x="150" y="275" width="200" height="15" fill="#e74c3c" rx="2"/>
                        <text x="250" y="286" text-anchor="middle" font-size="8" fill="white">5' Fragment</text>

                        <text x="375" y="286" text-anchor="middle" font-size="16" fill="#e74c3c">✂️</text>

                        <rect x="450" y="275" width="200" height="15" fill="#e74c3c" rx="2"/>
                        <text x="550" y="286" text-anchor="middle" font-size="8" fill="white">3' Fragment</text>

                        <text x="400" y="305" text-anchor="middle" font-size="11" fill="#333">Blunt-end double-strand break</text>

                        <!-- Step 4: NHEJ repair -->
                        <rect x="50" y="340" width="700" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="400" y="360" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">Step 4: NHEJ Repair with TdT Activity</text>

                        <!-- Repair outcomes -->
                        <rect x="100" y="375" width="150" height="15" fill="#9b59b6" rx="2"/>
                        <text x="175" y="386" text-anchor="middle" font-size="8" fill="white">Deletion</text>

                        <rect x="270" y="375" width="150" height="15" fill="#f39c12" rx="2"/>
                        <text x="345" y="386" text-anchor="middle" font-size="8" fill="white">Insertion</text>

                        <rect x="440" y="375" width="150" height="15" fill="#e74c3c" rx="2"/>
                        <text x="515" y="386" text-anchor="middle" font-size="8" fill="white">Deletion + Insertion</text>

                        <text x="400" y="405" text-anchor="middle" font-size="11" fill="#333">Random NHEJ outcomes create unique barcodes</text>
                    </svg>
                </div>

                <h3>TdT-Mediated Random Insertions</h3>
                <p>Terminal deoxynucleotidyl transferase (TdT) activity during NHEJ repair creates characteristic insertion patterns:</p>

                <div class="editing-example">
                    <h4>TdT Insertion Mechanism</h4>
                    <p><strong>TdT Activity:</strong> Adds random nucleotides to 3' ends of broken DNA</p>
                    $$P(\text{insertion length} = n) \propto e^{-\lambda n}$$
                    <p>Where $\lambda$ controls the exponential decay of insertion length probability</p>

                    <div class="mechanism-box">
                        <strong>Insertion Characteristics:</strong>
                        <ul>
                            <li><strong>Length Distribution:</strong> Exponential with mean ~2-4 nucleotides</li>
                            <li><strong>Nucleotide Bias:</strong> Slight preference for G and C</li>
                            <li><strong>Position Specificity:</strong> Occurs at double-strand break sites</li>
                            <li><strong>Stochastic Nature:</strong> Each editing event is unique</li>
                        </ul>
                    </div>
                </div>

                <h3>Editing Efficiency Calculation</h3>
                <p>The pipeline calculates editing efficiency by comparing sequences to the unedited reference:</p>

                <div class="editing-example">
                    <h4>Editing Detection Algorithm</h4>
                    <div class="code-block">
def check_editing(df, template):
    if template.startswith("cCARLIN"):
        CARLIN_seq = CA_CARLIN
    elif template.startswith("Tigre"):
        CARLIN_seq = TA_CARLIN
    elif template.startswith("Rosa"):
        CARLIN_seq = RA_CARLIN

    # Check if sequence matches unedited reference
    df['edited'] = df['clone_id'].apply(
        lambda x: not (CARLIN_seq.startswith(x))
    )

    # Calculate editing efficiency per cell
    editing_efficiency = df.groupby('cell_id').agg({
        'edited': 'mean'
    }).mean()['edited']

    return df
                    </div>

                    <p><strong>Editing Efficiency Formula:</strong></p>
                    $$\text{Editing Efficiency} = \frac{\text{Number of Edited Sequences}}{\text{Total Sequences Analyzed}}$$
                </div>

                <div class="key-insight">
                    <strong>🎯 Editing Strategy:</strong> The CARLIN system is designed to achieve ~30-70% editing efficiency, providing sufficient barcode diversity while maintaining enough unedited sequences for normalization and quality control.
                </div>
            </div>
        </div>

        <!-- Section 4: Three-Array System -->
        <div class="section" id="arrays">
            <div class="section-header">
                🎯 4. Three-Array System Architecture
            </div>
            <div class="section-content">
                <h3>DARLIN Three-Array Integration Strategy</h3>
                <p>The DARLIN system integrates three independent CARLIN arrays at different genomic loci to create a combinatorial barcode system:</p>

                <div class="svg-container">
                    <svg width="800" height="550" viewBox="0 0 800 550">
                        <rect width="800" height="550" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">DARLIN Three-Array System</text>

                        <!-- Genomic context -->
                        <rect x="50" y="60" width="700" height="100" fill="#f3e5f5" stroke="#9b59b6" stroke-width="3" rx="12"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#9b59b6">Genomic Integration Sites</text>

                        <!-- Chromosome representations -->
                        <rect x="100" y="105" width="180" height="45" fill="#e74c3c" rx="8"/>
                        <text x="190" y="120" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Chromosome 11</text>
                        <text x="190" y="135" text-anchor="middle" font-size="9" fill="white">Col1a1 locus → CA Array</text>
                        <text x="190" y="148" text-anchor="middle" font-size="8" fill="white">Collagen gene integration</text>

                        <rect x="310" y="105" width="180" height="45" fill="#f39c12" rx="8"/>
                        <text x="400" y="120" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Chromosome 15</text>
                        <text x="400" y="135" text-anchor="middle" font-size="9" fill="white">Tigre locus → TA Array</text>
                        <text x="400" y="148" text-anchor="middle" font-size="8" fill="white">Safe harbor integration</text>

                        <rect x="520" y="105" width="180" height="45" fill="#9b59b6" rx="8"/>
                        <text x="610" y="120" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Chromosome 6</text>
                        <text x="610" y="135" text-anchor="middle" font-size="9" fill="white">Rosa26 locus → RA Array</text>
                        <text x="610" y="148" text-anchor="middle" font-size="8" fill="white">Safe harbor integration</text>

                        <!-- Array editing process -->
                        <rect x="50" y="180" width="700" height="150" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="12"/>
                        <text x="400" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Simultaneous Array Editing</text>

                        <!-- Individual arrays -->
                        <rect x="100" y="230" width="180" height="80" fill="#e74c3c" rx="8"/>
                        <text x="190" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="white">CA Array Editing</text>
                        <rect x="120" y="265" width="140" height="12" fill="#ffcdd2" rx="2"/>
                        <text x="190" y="274" text-anchor="middle" font-size="8" fill="#333">Original: 364 bp</text>
                        <rect x="120" y="285" width="100" height="12" fill="#f44336" rx="2"/>
                        <text x="170" y="294" text-anchor="middle" font-size="8" fill="white">Edited: Variable</text>

                        <rect x="310" y="230" width="180" height="80" fill="#f39c12" rx="8"/>
                        <text x="400" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="white">TA Array Editing</text>
                        <rect x="330" y="265" width="140" height="12" fill="#ffe0b2" rx="2"/>
                        <text x="400" y="274" text-anchor="middle" font-size="8" fill="#333">Original: 320 bp</text>
                        <rect x="330" y="285" width="110" height="12" fill="#ff9800" rx="2"/>
                        <text x="385" y="294" text-anchor="middle" font-size="8" fill="white">Edited: Variable</text>

                        <rect x="520" y="230" width="180" height="80" fill="#9b59b6" rx="8"/>
                        <text x="610" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="white">RA Array Editing</text>
                        <rect x="540" y="265" width="140" height="12" fill="#e1bee7" rx="2"/>
                        <text x="610" y="274" text-anchor="middle" font-size="8" fill="#333">Original: 318 bp</text>
                        <rect x="540" y="285" width="120" height="12" fill="#9c27b0" rx="2"/>
                        <text x="600" y="294" text-anchor="middle" font-size="8" fill="white">Edited: Variable</text>

                        <!-- Combinatorial barcode -->
                        <rect x="50" y="350" width="700" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="400" y="375" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Combinatorial Barcode Formation</text>

                        <!-- Final barcode -->
                        <rect x="200" y="395" width="400" height="25" fill="#3498db" rx="5"/>
                        <text x="400" y="412" text-anchor="middle" font-size="11" fill="white">Unique Clone ID = CA_allele + TA_allele + RA_allele</text>

                        <!-- Arrows showing process flow -->
                        <path d="M 400 170 L 400 180" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>
                        <path d="M 400 340 L 400 350" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>

                        <!-- Diversity calculation -->
                        <rect x="150" y="450" width="500" height="30" fill="#d4edda" stroke="#27ae60" rx="5"/>
                        <text x="400" y="470" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Theoretical Diversity: 10⁶ to 10⁸ unique barcodes</text>

                        <defs>
                            <marker id="arrowhead1" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Array-Specific Characteristics</h3>
                <p>Each array has unique properties that contribute to the overall barcode diversity:</p>

                <table class="mutation-table">
                    <thead>
                        <tr>
                            <th>Array</th>
                            <th>Genomic Locus</th>
                            <th>Expression Pattern</th>
                            <th>Editing Characteristics</th>
                            <th>Typical Alleles</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>CA (cCARLIN)</strong></td>
                            <td>Col1a1 gene</td>
                            <td>Ubiquitous, high level</td>
                            <td>High editing efficiency</td>
                            <td>~10,000 unique alleles</td>
                        </tr>
                        <tr>
                            <td><strong>TA (Tigre)</strong></td>
                            <td>Tigre safe harbor</td>
                            <td>Controlled, moderate</td>
                            <td>Moderate editing efficiency</td>
                            <td>~8,000 unique alleles</td>
                        </tr>
                        <tr>
                            <td><strong>RA (Rosa26)</strong></td>
                            <td>Rosa26 safe harbor</td>
                            <td>Constitutive, stable</td>
                            <td>Consistent editing</td>
                            <td>~7,000 unique alleles</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Combinatorial Barcode Mathematics</h3>
                <div class="editing-example">
                    <h4>Barcode Diversity Calculation</h4>
                    <p><strong>Independent Array Editing:</strong></p>
                    $$\text{Total Diversity} = N_{CA} \times N_{TA} \times N_{RA}$$

                    <p><strong>Practical Example:</strong></p>
                    $$\text{Diversity} = 10,000 \times 8,000 \times 7,000 = 5.6 \times 10^{14}$$

                    <p><strong>Effective Diversity (with editing efficiency ~50%):</strong></p>
                    $$\text{Effective} = 0.5^3 \times \text{Total Diversity} = 7 \times 10^{13}$$

                    <p>This provides more than sufficient resolution for lineage tracing in any biological system.</p>
                </div>

                <div class="key-insight">
                    <strong>🚀 System Advantage:</strong> The three-array system provides exponentially increased barcode diversity compared to single-array systems, enabling high-resolution lineage tracing in complex developmental and disease contexts.
                </div>
            </div>
        </div>

        <!-- Section 5: Mutation Patterns -->
        <div class="section" id="mutations">
            <div class="section-header">
                🧪 5. Mutation Patterns and Classification
            </div>
            <div class="section-content">
                <h3>Mutation Nomenclature System</h3>
                <p>CARLIN/DARLIN uses a systematic nomenclature to describe all types of editing events:</p>

                <div class="svg-container">
                    <svg width="800" height="450" viewBox="0 0 800 450">
                        <rect width="800" height="450" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">CARLIN Mutation Nomenclature</text>

                        <!-- Deletion example -->
                        <rect x="50" y="60" width="700" height="80" fill="#ffebee" stroke="#e74c3c" stroke-width="3" rx="12"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">Deletion Mutations</text>

                        <!-- Deletion visualization -->
                        <rect x="100" y="105" width="600" height="25" fill="white" stroke="#e74c3c" rx="5"/>

                        <!-- Original sequence -->
                        <rect x="120" y="110" width="40" height="15" fill="#27ae60" rx="2"/>
                        <text x="140" y="121" text-anchor="middle" font-size="8" fill="white">...20</text>

                        <rect x="170" y="110" width="40" height="15" fill="#f39c12" rx="2"/>
                        <text x="190" y="121" text-anchor="middle" font-size="8" fill="white">21</text>

                        <rect x="220" y="110" width="40" height="15" fill="#f39c12" rx="2"/>
                        <text x="240" y="121" text-anchor="middle" font-size="8" fill="white">22</text>

                        <rect x="270" y="110" width="40" height="15" fill="#e74c3c" rx="2"/>
                        <text x="290" y="121" text-anchor="middle" font-size="8" fill="white">23</text>

                        <text x="320" y="121" text-anchor="middle" font-size="10" fill="#e74c3c">DELETED</text>

                        <rect x="420" y="110" width="40" height="15" fill="#e74c3c" rx="2"/>
                        <text x="440" y="121" text-anchor="middle" font-size="8" fill="white">268</text>

                        <rect x="470" y="110" width="40" height="15" fill="#27ae60" rx="2"/>
                        <text x="490" y="121" text-anchor="middle" font-size="8" fill="white">269</text>

                        <rect x="520" y="110" width="40" height="15" fill="#27ae60" rx="2"/>
                        <text x="540" y="121" text-anchor="middle" font-size="8" fill="white">270...</text>

                        <text x="400" y="150" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Nomenclature: 23_268del</text>

                        <!-- Insertion example -->
                        <rect x="50" y="180" width="700" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="12"/>
                        <text x="400" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Insertion Mutations</text>

                        <!-- Insertion visualization -->
                        <rect x="100" y="225" width="600" height="25" fill="white" stroke="#27ae60" rx="5"/>

                        <rect x="200" y="230" width="40" height="15" fill="#3498db" rx="2"/>
                        <text x="220" y="241" text-anchor="middle" font-size="8" fill="white">264</text>

                        <rect x="250" y="230" width="40" height="15" fill="#3498db" rx="2"/>
                        <text x="270" y="241" text-anchor="middle" font-size="8" fill="white">265</text>

                        <rect x="300" y="230" width="60" height="15" fill="#27ae60" rx="2"/>
                        <text x="330" y="241" text-anchor="middle" font-size="8" fill="white">+ATCG</text>

                        <rect x="370" y="230" width="40" height="15" fill="#3498db" rx="2"/>
                        <text x="390" y="241" text-anchor="middle" font-size="8" fill="white">266</text>

                        <rect x="420" y="230" width="40" height="15" fill="#3498db" rx="2"/>
                        <text x="440" y="241" text-anchor="middle" font-size="8" fill="white">267</text>

                        <text x="400" y="270" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Nomenclature: 265_266insATCG</text>

                        <!-- Complex mutation example -->
                        <rect x="50" y="300" width="700" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="3" rx="12"/>
                        <text x="400" y="325" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Complex Mutations (Delins)</text>

                        <text x="400" y="350" text-anchor="middle" font-size="12" fill="#333">Deletion + Insertion at same site</text>
                        <text x="400" y="370" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Example: 23_265delinsG</text>

                        <!-- Frequency information -->
                        <rect x="100" y="400" width="600" height="30" fill="#d4edda" stroke="#27ae60" rx="5"/>
                        <text x="400" y="420" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Most Common: Small deletions (1-5 bp) and single nucleotide insertions</text>
                    </svg>
                </div>

                <h3>Real Mutation Examples from Reference Data</h3>
                <p>Analysis of the reference allele bank reveals characteristic mutation patterns:</p>

                <table class="mutation-table">
                    <thead>
                        <tr>
                            <th>Mutation Type</th>
                            <th>Example Allele</th>
                            <th>Frequency</th>
                            <th>Interpretation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Simple Deletion</strong></td>
                            <td>23_268del</td>
                            <td>0.0150 (1.5%)</td>
                            <td>5 bp deletion from position 23-268</td>
                        </tr>
                        <tr>
                            <td><strong>Simple Deletion</strong></td>
                            <td>23_265del</td>
                            <td>0.0135 (1.35%)</td>
                            <td>2 bp deletion from position 23-265</td>
                        </tr>
                        <tr>
                            <td><strong>Single Insertion</strong></td>
                            <td>23_265delinsG</td>
                            <td>0.0100 (1.0%)</td>
                            <td>2 bp deletion + G insertion</td>
                        </tr>
                        <tr>
                            <td><strong>Complex Delins</strong></td>
                            <td>23_238del,265_266insG</td>
                            <td>0.0021 (0.21%)</td>
                            <td>Multiple editing events</td>
                        </tr>
                        <tr>
                            <td><strong>Unedited</strong></td>
                            <td>[]</td>
                            <td>1.0000 (Reference)</td>
                            <td>No editing occurred</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Mutation Frequency Distribution</h3>
                <p>The distribution of mutation frequencies follows characteristic patterns:</p>

                <div class="editing-example">
                    <h4>Power Law Distribution of Allele Frequencies</h4>
                    $$P(\text{frequency} = f) \propto f^{-\alpha}$$

                    <p>Where $\alpha \approx 1.5-2.0$ for most CARLIN datasets</p>

                    <div class="mechanism-box">
                        <strong>Key Frequency Metrics:</strong>
                        <ul>
                            <li><strong>Singleton Fraction:</strong> ~60-80% of alleles appear only once</li>
                            <li><strong>High-Frequency Alleles:</strong> <1% of alleles account for >50% of reads</li>
                            <li><strong>Editing Efficiency:</strong> Typically 30-70% depending on conditions</li>
                            <li><strong>Null Allele Frequency:</strong> Unedited sequences serve as internal control</li>
                        </ul>
                    </div>
                </div>

                <h3>Mutation Length Analysis</h3>
                <div class="algorithm-box">
                    <h4>Insertion and Deletion Length Distributions</h4>
                    <p><strong>Deletion Lengths:</strong> Exponential distribution with preference for small deletions</p>
                    $$P(\text{deletion length} = n) = \lambda e^{-\lambda n}$$

                    <p><strong>Insertion Lengths:</strong> TdT-mediated, typically 1-4 nucleotides</p>
                    $$P(\text{insertion length} = n) = \mu e^{-\mu n}$$

                    <p>Where $\lambda > \mu$, indicating deletions are generally larger than insertions</p>
                </div>

                <div class="key-insight">
                    <strong>🔬 Biological Significance:</strong> The characteristic mutation patterns reflect the underlying molecular mechanisms of NHEJ repair and TdT activity, providing insights into DNA repair processes while serving as unique cellular barcodes.
                </div>
            </div>
        </div>

        <!-- Section 6: Output Analysis -->
        <div class="section" id="analysis">
            <div class="section-header">
                📊 6. Output Sequence Analysis
            </div>
            <div class="section-content">
                <h3>Allele Annotation and Frequency Calculation</h3>
                <p>The MosaicLineage package provides comprehensive tools for analyzing edited sequences and calculating allele frequencies:</p>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <rect width="800" height="500" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Allele Analysis Workflow</text>

                        <!-- Raw sequence input -->
                        <rect x="50" y="60" width="700" height="60" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Input: Raw Sequencing Data</text>
                        <text x="400" y="105" text-anchor="middle" font-size="12" fill="#333">FASTQ files with UMI and cell barcode information</text>

                        <!-- Processing steps -->
                        <rect x="50" y="140" width="160" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="130" y="165" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Step 1</text>
                        <text x="130" y="180" text-anchor="middle" font-size="10" fill="#333">Sequence</text>
                        <text x="130" y="195" text-anchor="middle" font-size="10" fill="#333">Alignment</text>
                        <text x="130" y="210" text-anchor="middle" font-size="9" fill="#666">vs Reference</text>

                        <rect x="230" y="140" width="160" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="310" y="165" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Step 2</text>
                        <text x="310" y="180" text-anchor="middle" font-size="10" fill="#333">Mutation</text>
                        <text x="310" y="195" text-anchor="middle" font-size="10" fill="#333">Detection</text>
                        <text x="310" y="210" text-anchor="middle" font-size="9" fill="#666">Indel calling</text>

                        <rect x="410" y="140" width="160" height="80" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" rx="10"/>
                        <text x="490" y="165" text-anchor="middle" font-size="12" font-weight="bold" fill="#9b59b6">Step 3</text>
                        <text x="490" y="180" text-anchor="middle" font-size="10" fill="#333">Allele</text>
                        <text x="490" y="195" text-anchor="middle" font-size="10" fill="#333">Annotation</text>
                        <text x="490" y="210" text-anchor="middle" font-size="9" fill="#666">Nomenclature</text>

                        <rect x="590" y="140" width="160" height="80" fill="#ffebee" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="670" y="165" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Step 4</text>
                        <text x="670" y="180" text-anchor="middle" font-size="10" fill="#333">Frequency</text>
                        <text x="670" y="195" text-anchor="middle" font-size="10" fill="#333">Calculation</text>
                        <text x="670" y="210" text-anchor="middle" font-size="9" fill="#666">UMI counting</text>

                        <!-- Output data structure -->
                        <rect x="50" y="250" width="700" height="120" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="400" y="275" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Output: Annotated Allele DataFrame</text>

                        <!-- DataFrame columns -->
                        <rect x="80" y="295" width="100" height="60" fill="#3498db" rx="8"/>
                        <text x="130" y="315" text-anchor="middle" font-size="10" fill="white">allele</text>
                        <text x="130" y="330" text-anchor="middle" font-size="9" fill="white">Mutation ID</text>
                        <text x="130" y="345" text-anchor="middle" font-size="8" fill="white">e.g., 23_268del</text>

                        <rect x="200" y="295" width="100" height="60" fill="#27ae60" rx="8"/>
                        <text x="250" y="315" text-anchor="middle" font-size="10" fill="white">UMI_count</text>
                        <text x="250" y="330" text-anchor="middle" font-size="9" fill="white">Frequency</text>
                        <text x="250" y="345" text-anchor="middle" font-size="8" fill="white">Raw counts</text>

                        <rect x="320" y="295" width="100" height="60" fill="#f39c12" rx="8"/>
                        <text x="370" y="315" text-anchor="middle" font-size="10" fill="white">sample_count</text>
                        <text x="370" y="330" text-anchor="middle" font-size="9" fill="white">Prevalence</text>
                        <text x="370" y="345" text-anchor="middle" font-size="8" fill="white">Cross-sample</text>

                        <rect x="440" y="295" width="120" height="60" fill="#9b59b6" rx="8"/>
                        <text x="500" y="315" text-anchor="middle" font-size="10" fill="white">normalized_count</text>
                        <text x="500" y="330" text-anchor="middle" font-size="9" fill="white">Reference freq.</text>
                        <text x="500" y="345" text-anchor="middle" font-size="8" fill="white">Allele bank</text>

                        <rect x="580" y="295" width="100" height="60" fill="#e74c3c" rx="8"/>
                        <text x="630" y="315" text-anchor="middle" font-size="10" fill="white">sample</text>
                        <text x="630" y="330" text-anchor="middle" font-size="9" fill="white">Source ID</text>
                        <text x="630" y="345" text-anchor="middle" font-size="8" fill="white">Experiment</text>

                        <!-- Analysis metrics -->
                        <rect x="100" y="390" width="600" height="80" fill="white" stroke="#34495e" stroke-width="2" rx="10"/>
                        <text x="400" y="410" text-anchor="middle" font-size="14" font-weight="bold" fill="#34495e">Key Analysis Metrics</text>

                        <text x="200" y="430" text-anchor="middle" font-size="11" fill="#333">Shannon Diversity</text>
                        <text x="200" y="445" text-anchor="middle" font-size="10" fill="#666">Barcode complexity</text>
                        <text x="200" y="460" text-anchor="middle" font-size="9" fill="#666">H = -Σ p log₂ p</text>

                        <text x="400" y="430" text-anchor="middle" font-size="11" fill="#333">Editing Efficiency</text>
                        <text x="400" y="445" text-anchor="middle" font-size="10" fill="#666">Fraction edited</text>
                        <text x="400" y="460" text-anchor="middle" font-size="9" fill="#666">Edited / Total</text>

                        <text x="600" y="430" text-anchor="middle" font-size="11" fill="#333">Clonal Diversity</text>
                        <text x="600" y="445" text-anchor="middle" font-size="10" fill="#666">Effective alleles</text>
                        <text x="600" y="460" text-anchor="middle" font-size="9" fill="#666">2^H metric</text>
                    </svg>
                </div>

                <h3>Reference Allele Bank Construction</h3>
                <p>The package includes comprehensive reference allele banks built from controlled experiments:</p>

                <div class="editing-example">
                    <h4>Allele Bank Experiment Design</h4>
                    <p>Reference alleles were generated through dedicated experiments designed to measure intrinsic allele generation frequencies:</p>

                    <div class="mechanism-box">
                        <strong>Experimental Strategy:</strong>
                        <ol>
                            <li><strong>Controlled Induction:</strong> Doxycycline treatment of founder mice</li>
                            <li><strong>Granulocyte Isolation:</strong> Minimize clonal expansion effects</li>
                            <li><strong>Deep Sequencing:</strong> Comprehensive allele detection</li>
                            <li><strong>Frequency Normalization:</strong> Account for sequencing depth and locus expression</li>
                            <li><strong>Cross-Sample Validation:</strong> Multiple biological replicates</li>
                        </ol>
                    </div>
                </div>

                <h3>Quality Control Metrics</h3>
                <div class="algorithm-box">
                    <h4>Allele Quality Assessment</h4>
                    <p><strong>Reliable Allele Selection Criteria:</strong></p>
                    $$\text{Reliable if: } \rho < \rho^* \text{ AND } m < m^*$$

                    <p>Where:</p>
                    <ul>
                        <li>$\rho$ = normalized_count (generation probability)</li>
                        <li>$\rho^*$ = frequency cutoff (typically 10⁻⁴)</li>
                        <li>$m$ = sample_count (cross-sample prevalence)</li>
                        <li>$m^*$ = sample cutoff (typically 2-3)</li>
                    </ul>

                    <div class="function-signature">
# Quality filtering implementation
def filter_reliable_alleles(df_allele, freq_cutoff=1e-4, sample_cutoff=3):
    """
    Filter alleles for reliable lineage tracing

    Parameters:
    - df_allele: Allele frequency DataFrame
    - freq_cutoff: Maximum normalized_count threshold
    - sample_cutoff: Maximum sample_count threshold

    Returns:
    - Filtered DataFrame with reliable alleles only
    """
    reliable_mask = (
        (df_allele['normalized_count'] < freq_cutoff) &
        (df_allele['sample_count'] < sample_cutoff)
    )
    return df_allele[reliable_mask]
                    </div>
                </div>

                <div class="key-insight">
                    <strong>💡 Analysis Innovation:</strong> The reference allele bank approach enables quantitative assessment of allele reliability, allowing researchers to select optimal barcodes for lineage tracing based on their intrinsic generation probabilities.
                </div>
            </div>
        </div>

        <!-- Section 7: Applications -->
        <div class="section" id="applications">
            <div class="section-header">
                🌟 7. Applications and Biological Insights
            </div>
            <div class="section-content">
                <h3>Lineage Tracing in Development and Disease</h3>
                <p>CARLIN/DARLIN sequences enable unprecedented resolution in studying cellular lineage relationships:</p>

                <div class="svg-container">
                    <svg width="800" height="450" viewBox="0 0 800 450">
                        <rect width="800" height="450" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">DARLIN Applications in Biology</text>

                        <!-- Application categories -->
                        <rect x="50" y="60" width="180" height="100" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="12"/>
                        <text x="140" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Hematopoiesis</text>
                        <text x="140" y="105" text-anchor="middle" font-size="10" fill="#333">• HSC differentiation</text>
                        <text x="140" y="120" text-anchor="middle" font-size="10" fill="#333">• Lineage commitment</text>
                        <text x="140" y="135" text-anchor="middle" font-size="10" fill="#333">• Clonal dynamics</text>
                        <text x="140" y="150" text-anchor="middle" font-size="10" fill="#333">• Migration patterns</text>

                        <rect x="250" y="60" width="180" height="100" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="340" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">Development</text>
                        <text x="340" y="105" text-anchor="middle" font-size="10" fill="#333">• Organ formation</text>
                        <text x="340" y="120" text-anchor="middle" font-size="10" fill="#333">• Tissue patterning</text>
                        <text x="340" y="135" text-anchor="middle" font-size="10" fill="#333">• Cell fate decisions</text>
                        <text x="340" y="150" text-anchor="middle" font-size="10" fill="#333">• Temporal dynamics</text>

                        <rect x="450" y="60" width="180" height="100" fill="#fff3cd" stroke="#f39c12" stroke-width="3" rx="12"/>
                        <text x="540" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">Disease</text>
                        <text x="540" y="105" text-anchor="middle" font-size="10" fill="#333">• Cancer progression</text>
                        <text x="540" y="120" text-anchor="middle" font-size="10" fill="#333">• Metastasis tracking</text>
                        <text x="540" y="135" text-anchor="middle" font-size="10" fill="#333">• Drug resistance</text>
                        <text x="540" y="150" text-anchor="middle" font-size="10" fill="#333">• Clonal evolution</text>

                        <rect x="650" y="60" width="100" height="100" fill="#f3e5f5" stroke="#9b59b6" stroke-width="3" rx="12"/>
                        <text x="700" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#9b59b6">Aging</text>
                        <text x="700" y="105" text-anchor="middle" font-size="10" fill="#333">• Stem cell</text>
                        <text x="700" y="120" text-anchor="middle" font-size="10" fill="#333">exhaustion</text>
                        <text x="700" y="135" text-anchor="middle" font-size="10" fill="#333">• Clonal</text>
                        <text x="700" y="150" text-anchor="middle" font-size="10" fill="#333">expansion</text>

                        <!-- Technical advantages -->
                        <rect x="50" y="190" width="700" height="120" fill="white" stroke="#34495e" stroke-width="2" rx="12"/>
                        <text x="400" y="215" text-anchor="middle" font-size="16" font-weight="bold" fill="#34495e">Technical Advantages of DARLIN</text>

                        <rect x="80" y="240" width="160" height="60" fill="#e74c3c" rx="8"/>
                        <text x="160" y="260" text-anchor="middle" font-size="11" font-weight="bold" fill="white">High Resolution</text>
                        <text x="160" y="275" text-anchor="middle" font-size="9" fill="white">10⁶-10⁸ barcodes</text>
                        <text x="160" y="290" text-anchor="middle" font-size="9" fill="white">Single-cell precision</text>

                        <rect x="260" y="240" width="160" height="60" fill="#27ae60" rx="8"/>
                        <text x="340" y="260" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Temporal Stability</text>
                        <text x="340" y="275" text-anchor="middle" font-size="9" fill="white">Permanent marking</text>
                        <text x="340" y="290" text-anchor="middle" font-size="9" fill="white">Heritable barcodes</text>

                        <rect x="440" y="240" width="160" height="60" fill="#3498db" rx="8"/>
                        <text x="520" y="260" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Multi-omics</text>
                        <text x="520" y="275" text-anchor="middle" font-size="9" fill="white">RNA + lineage</text>
                        <text x="520" y="290" text-anchor="middle" font-size="9" fill="white">Simultaneous readout</text>

                        <rect x="620" y="240" width="120" height="60" fill="#9b59b6" rx="8"/>
                        <text x="680" y="260" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Quantitative</text>
                        <text x="680" y="275" text-anchor="middle" font-size="9" fill="white">Statistical power</text>
                        <text x="680" y="290" text-anchor="middle" font-size="9" fill="white">Robust analysis</text>

                        <!-- Biological insights -->
                        <rect x="100" y="330" width="600" height="100" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="400" y="355" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Key Biological Discoveries</text>

                        <text x="200" y="375" font-size="11" font-weight="bold" fill="#333">Hematopoietic Insights:</text>
                        <text x="220" y="390" font-size="10" fill="#333">• HSC heterogeneity revealed</text>
                        <text x="220" y="405" font-size="10" fill="#333">• Lineage bias quantified</text>
                        <text x="220" y="420" font-size="10" fill="#333">• Migration patterns mapped</text>

                        <text x="500" y="375" font-size="11" font-weight="bold" fill="#333">Technical Advances:</text>
                        <text x="520" y="390" font-size="10" fill="#333">• Single-cell resolution</text>
                        <text x="520" y="405" font-size="10" fill="#333">• Temporal reconstruction</text>
                        <text x="520" y="420" font-size="10" fill="#333">• Quantitative lineage maps</text>
                    </svg>
                </div>

                <h3>Single-Cell and Bulk Integration</h3>
                <p>DARLIN enables seamless integration of single-cell and bulk sequencing approaches:</p>

                <div class="mechanism-box">
                    <strong>🔗 Multi-Modal Analysis:</strong>
                    <ul>
                        <li><strong>Single-Cell RNA-seq + DARLIN:</strong> Simultaneous gene expression and lineage information</li>
                        <li><strong>Bulk RNA-seq + DARLIN:</strong> Population-level lineage dynamics</li>
                        <li><strong>Camellia-seq Integration:</strong> Multi-omics lineage tracing</li>
                        <li><strong>Temporal Sampling:</strong> Longitudinal lineage reconstruction</li>
                    </ul>
                </div>

                <h3>Computational Analysis Pipeline</h3>
                <div class="editing-example">
                    <h4>Complete Analysis Workflow</h4>
                    <div class="code-block">
# Example DARLIN analysis workflow
import mosaiclineage.help_functions as ml

# 1. Load multi-array data
adata = ml.load_all_samples_to_adata(
    SampleList=['HSC', 'MPP', 'MyP'],
    file_path='DARLIN_results/',
    df_ref=reference_alleles,
    frequency_cutoff=1e-4
)

# 2. Calculate lineage metrics
shannon_div = ml.calculate_shannon_diversity(adata.obs['clone_id'])
coupling_matrix = ml.get_fate_count_coupling(adata.obsm['X_clone'])

# 3. Analyze migration patterns
shared_fractions = ml.calculate_shared_clone_fractions(
    adata.obs['clone_id'],
    adata.obs['tissue']
)

# 4. Generate comprehensive visualizations
ml.custom_conditional_heatmap(
    coupling_matrix,
    cell_types=['HSC', 'MPP', 'MyP', 'Mature']
)
                    </div>
                </div>

                <h3>Future Directions and Extensions</h3>
                <div class="key-insight">
                    <strong>🔮 Future Applications:</strong>
                    <ul>
                        <li><strong>Spatial Lineage Tracing:</strong> Integration with spatial transcriptomics</li>
                        <li><strong>Temporal Resolution:</strong> Time-course lineage reconstruction</li>
                        <li><strong>Disease Modeling:</strong> Cancer evolution and metastasis tracking</li>
                        <li><strong>Therapeutic Monitoring:</strong> Treatment response assessment</li>
                        <li><strong>Regenerative Medicine:</strong> Stem cell therapy evaluation</li>
                    </ul>
                </div>

                <div class="algorithm-box">
                    <h4>Homoplasy Probability Assessment</h4>
                    <p>Critical for ensuring barcode uniqueness:</p>
                    $$P(\text{homoplasy}) = 1 - e^{-\frac{N(N-1)}{2} \cdot \rho}$$

                    <p>Where:</p>
                    <ul>
                        <li>$N$ = population size being barcoded</li>
                        <li>$\rho$ = allele generation probability</li>
                    </ul>

                    <p>This formula guides selection of reliable alleles for lineage tracing experiments.</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding: 50px; color: white; background: rgba(0,0,0,0.1); border-radius: 20px; margin-top: 30px;">
            <p style="font-size: 1.4em; font-weight: bold;">CARLIN/DARLIN Sequence Biology Tutorial</p>
            <p style="margin-top: 12px;">Comprehensive Guide to Biological Sequences and Editing Mechanisms</p>
            <p style="margin-top: 18px; font-size: 1.05em;">Based on: Li et al. (2023) Cell - DARLIN Project</p>
            <p style="margin-top: 10px; font-size: 1.05em;">MosaicLineage Package Analysis</p>
        </div>
    </div>
</body>
</html>
