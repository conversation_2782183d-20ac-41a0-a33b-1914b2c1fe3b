[tool.poetry]
name = "mosaiclineage"
version = "0.2.0"
description = "A collection of functions designed to analyze lineage tracing data from different systems"
authors = ["<PERSON><PERSON><PERSON><PERSON> <wang<PERSON><PERSON><PERSON>@westlake.edu.cn>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.6"

[tool.poetry.dev-dependencies]


[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
