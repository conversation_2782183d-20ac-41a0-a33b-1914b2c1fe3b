<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DARLIN: Advanced Lineage Tracing Technology - Step by Step Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-bg: #34495e;
            --text-color: #2c3e50;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .outline {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-left: 5px solid var(--secondary-color);
        }

        .outline h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .outline ol {
            padding-left: 20px;
        }

        .outline li {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .outline li ul {
            margin-top: 5px;
            padding-left: 20px;
        }

        .outline li ul li {
            font-size: 1em;
            color: #666;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-left: 5px solid var(--accent-color);
        }

        .section-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
            padding: 20px 30px;
            font-size: 1.5em;
            font-weight: bold;
        }

        .section-content {
            padding: 30px;
        }

        .step-box {
            background: var(--light-bg);
            border-left: 4px solid var(--success-color);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .step-number {
            background: var(--success-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid var(--warning-color);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 5px solid var(--warning-color);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            text-align: left;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .math-formula {
            background: #f8f9fa;
            border: 1px solid var(--border-color);
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            text-align: center;
        }

        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .key-point {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left: 4px solid var(--success-color);
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            max-width: 200px;
            z-index: 1000;
        }

        .navigation h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1em;
        }

        .navigation ul {
            list-style: none;
        }

        .navigation li {
            margin-bottom: 5px;
        }

        .navigation a {
            color: var(--secondary-color);
            text-decoration: none;
            font-size: 0.9em;
            display: block;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .navigation a:hover {
            background: var(--light-bg);
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <h3>Quick Navigation</h3>
        <ul>
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#technology">Core Technology</a></li>
            <li><a href="#architecture">System Architecture</a></li>
            <li><a href="#process">Step-by-Step Process</a></li>
            <li><a href="#applications">Applications</a></li>
            <li><a href="#advantages">Advantages</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>DARLIN Tutorial</h1>
            <p>DNA And RNA Lineage INformation - Advanced Single-Cell Lineage Tracing</p>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ol>
                <li><strong>Introduction to DARLIN</strong>
                    <ul>
                        <li>What is DARLIN and its purpose</li>
                        <li>Comparison with Cas9/CARLIN system</li>
                        <li>Key improvements and innovations</li>
                    </ul>
                </li>
                <li><strong>Core Technology Components</strong>
                    <ul>
                        <li>CRISPR-Cas9 genome editing</li>
                        <li>Terminal deoxynucleotidyl transferase (TdT)</li>
                        <li>Target arrays and barcode generation</li>
                    </ul>
                </li>
                <li><strong>DARLIN System Architecture</strong>
                    <ul>
                        <li>Three independent target arrays</li>
                        <li>Inducible expression system</li>
                        <li>Genetic integration sites</li>
                    </ul>
                </li>
                <li><strong>Step-by-Step DARLIN Process</strong>
                    <ul>
                        <li>Barcode induction with doxycycline</li>
                        <li>Cas9-TdT editing mechanism</li>
                        <li>Single-cell capture and analysis</li>
                    </ul>
                </li>
                <li><strong>Applications and Results</strong>
                    <ul>
                        <li>Hematopoietic stem cell fate mapping</li>
                        <li>Cell migration studies</li>
                        <li>Camellia-seq multi-omics integration</li>
                    </ul>
                </li>
                <li><strong>Advantages and Limitations</strong>
                    <ul>
                        <li>Performance improvements</li>
                        <li>Current limitations</li>
                        <li>Future directions</li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 1: Introduction -->
        <div class="section" id="introduction">
            <div class="section-header">
                🧬 1. Introduction to DARLIN
            </div>
            <div class="section-content">
                <h3>What is DARLIN?</h3>
                <p><strong>DARLIN</strong> stands for <strong>DNA And RNA Lineage INformation</strong> - an advanced mouse model system for high-resolution cellular lineage tracing. It represents a significant improvement over previous lineage tracing technologies.</p>

                <div class="key-point">
                    <strong>🎯 Key Innovation:</strong> DARLIN combines CRISPR-Cas9 with Terminal deoxynucleotidyl transferase (TdT) to create an estimated <strong>10<sup>18</sup></strong> unique lineage barcodes - enough to label every cell in an adult mouse.
                </div>

                <h3>Why was DARLIN developed?</h3>
                <p>Previous lineage tracing systems faced three major challenges:</p>
                <ul>
                    <li><strong>Low barcode capture:</strong> Only ~10% of cells had usable lineage information</li>
                    <li><strong>Low editing efficiency:</strong> Limited barcode generation</li>
                    <li><strong>Barcode homoplasy:</strong> Same barcode appearing in unrelated cells</li>
                </ul>

                <div class="svg-container">
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Evolution of Lineage Tracing Technology</text>

                        <!-- Timeline -->
                        <line x1="100" y1="80" x2="700" y2="80" stroke="#34495e" stroke-width="3"/>

                        <!-- Traditional Methods -->
                        <circle cx="150" cy="80" r="8" fill="#e74c3c"/>
                        <text x="150" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Traditional</text>
                        <text x="150" y="120" text-anchor="middle" font-size="10" fill="#666">Dyes, Markers</text>
                        <rect x="100" y="140" width="100" height="60" fill="#ffebee" stroke="#e74c3c" rx="5"/>
                        <text x="150" y="160" text-anchor="middle" font-size="10" fill="#333">Limited cells</text>
                        <text x="150" y="175" text-anchor="middle" font-size="10" fill="#333">Pre-defined</text>
                        <text x="150" y="190" text-anchor="middle" font-size="10" fill="#333">populations</text>

                        <!-- Cas9/CARLIN -->
                        <circle cx="350" cy="80" r="8" fill="#f39c12"/>
                        <text x="350" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Cas9/CARLIN</text>
                        <text x="350" y="120" text-anchor="middle" font-size="10" fill="#666">CRISPR-based</text>
                        <rect x="300" y="140" width="100" height="80" fill="#fff3cd" stroke="#f39c12" rx="5"/>
                        <text x="350" y="160" text-anchor="middle" font-size="10" fill="#333">~10% cell coverage</text>
                        <text x="350" y="175" text-anchor="middle" font-size="10" fill="#333">Limited diversity</text>
                        <text x="350" y="190" text-anchor="middle" font-size="10" fill="#333">Deletion-prone</text>
                        <text x="350" y="205" text-anchor="middle" font-size="10" fill="#333">44K total alleles</text>

                        <!-- DARLIN -->
                        <circle cx="550" cy="80" r="8" fill="#27ae60"/>
                        <text x="550" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">DARLIN</text>
                        <text x="550" y="120" text-anchor="middle" font-size="10" fill="#666">Cas9-TdT Enhanced</text>
                        <rect x="500" y="140" width="100" height="100" fill="#e8f5e8" stroke="#27ae60" rx="5"/>
                        <text x="550" y="160" text-anchor="middle" font-size="10" fill="#333">~60% cell coverage</text>
                        <text x="550" y="175" text-anchor="middle" font-size="10" fill="#333">10¹⁸ barcodes</text>
                        <text x="550" y="190" text-anchor="middle" font-size="10" fill="#333">Insertion-rich</text>
                        <text x="550" y="205" text-anchor="middle" font-size="10" fill="#333">3 target arrays</text>
                        <text x="550" y="220" text-anchor="middle" font-size="10" fill="#333">Multi-omics ready</text>

                        <!-- Arrows -->
                        <path d="M 200 80 L 290 80" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 400 80 L 490 80" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>

                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>

                        <!-- Performance comparison -->
                        <text x="400" y="280" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Performance Comparison</text>

                        <!-- Cas9/CARLIN bar -->
                        <rect x="250" y="300" width="60" height="20" fill="#f39c12"/>
                        <text x="280" y="315" text-anchor="middle" font-size="10" fill="white">10%</text>
                        <text x="280" y="335" text-anchor="middle" font-size="12" fill="#333">Cas9/CARLIN</text>

                        <!-- DARLIN bar -->
                        <rect x="450" y="300" width="360" height="20" fill="#27ae60"/>
                        <text x="630" y="315" text-anchor="middle" font-size="10" fill="white">60%</text>
                        <text x="630" y="335" text-anchor="middle" font-size="12" fill="#333">DARLIN</text>

                        <text x="200" y="315" text-anchor="middle" font-size="10" fill="#666">Cell Coverage:</text>
                    </svg>
                </div>

                <h3>The Problem with Previous Systems</h3>
                <p>The original Cas9/CARLIN system, while innovative, had significant limitations:</p>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Aspect</th>
                            <th>Cas9/CARLIN</th>
                            <th>DARLIN Improvement</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Cell Coverage</strong></td>
                            <td>~10% of cells with usable barcodes</td>
                            <td>~60% of cells with usable barcodes</td>
                        </tr>
                        <tr>
                            <td><strong>Editing Efficiency</strong></td>
                            <td>30-50% across tissues</td>
                            <td>>90% across tissues</td>
                        </tr>
                        <tr>
                            <td><strong>Barcode Diversity</strong></td>
                            <td>~44,000 total alleles</td>
                            <td>>1.3 million alleles (estimated 10¹⁸)</td>
                        </tr>
                        <tr>
                            <td><strong>Editing Pattern</strong></td>
                            <td>Deletion-prone (2.5 deletions : 1.5 insertions)</td>
                            <td>Insertion-rich (1+ insertions : 1 deletion)</td>
                        </tr>
                    </tbody>
                </table>

                <div class="highlight-box">
                    <strong>💡 Key Insight:</strong> The main breakthrough came from recognizing that <em>insertions</em> create rare, unique barcodes while <em>deletions</em> create common, less informative barcodes. DARLIN's use of TdT dramatically increases insertion frequency.
                </div>
            </div>
        </div>

        <!-- Section 2: Core Technology -->
        <div class="section" id="technology">
            <div class="section-header">
                🔬 2. Core Technology Components
            </div>
            <div class="section-content">
                <h3>The CRISPR-Cas9 Foundation</h3>
                <p>DARLIN builds upon the CRISPR-Cas9 genome editing system, which can create precise cuts in DNA at specific target sequences. However, standard Cas9 editing has a bias toward deletions rather than insertions.</p>

                <div class="step-box">
                    <span class="step-number">1</span>
                    <strong>Standard Cas9 Editing:</strong> Creates double-strand breaks that are repaired by cellular machinery, often resulting in deletions that reduce barcode diversity.
                </div>

                <h3>Terminal deoxynucleotidyl Transferase (TdT) - The Game Changer</h3>
                <p>TdT is a template-independent DNA polymerase that can add random nucleotides to DNA ends. This is the key innovation in DARLIN.</p>

                <div class="svg-container">
                    <svg width="700" height="350" viewBox="0 0 700 350">
                        <!-- Background -->
                        <rect width="700" height="350" fill="#f8f9fa" rx="10"/>

                        <!-- Title -->
                        <text x="350" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Cas9 vs Cas9-TdT Editing Mechanisms</text>

                        <!-- Cas9 only section -->
                        <text x="175" y="55" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Standard Cas9</text>

                        <!-- DNA strand before -->
                        <rect x="50" y="70" width="250" height="15" fill="#3498db" rx="3"/>
                        <text x="175" y="82" text-anchor="middle" font-size="10" fill="white">Target DNA Sequence</text>

                        <!-- Cut site -->
                        <line x1="175" y1="95" x2="175" y2="110" stroke="#e74c3c" stroke-width="3"/>
                        <text x="175" y="125" text-anchor="middle" font-size="10" fill="#e74c3c">Cas9 Cut</text>

                        <!-- Repair outcome - deletion -->
                        <rect x="75" y="140" width="200" height="15" fill="#e74c3c" rx="3"/>
                        <text x="175" y="152" text-anchor="middle" font-size="10" fill="white">Deletion (Common)</text>
                        <text x="175" y="170" text-anchor="middle" font-size="9" fill="#666">Information Loss</text>

                        <!-- Cas9-TdT section -->
                        <text x="525" y="55" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Cas9-TdT Fusion</text>

                        <!-- DNA strand before -->
                        <rect x="400" y="70" width="250" height="15" fill="#3498db" rx="3"/>
                        <text x="525" y="82" text-anchor="middle" font-size="10" fill="white">Target DNA Sequence</text>

                        <!-- Cut site -->
                        <line x1="525" y1="95" x2="525" y2="110" stroke="#e74c3c" stroke-width="3"/>
                        <text x="525" y="125" text-anchor="middle" font-size="10" fill="#e74c3c">Cas9 Cut</text>

                        <!-- TdT action -->
                        <circle cx="525" cy="130" r="12" fill="#f39c12"/>
                        <text x="525" y="135" text-anchor="middle" font-size="8" fill="white">TdT</text>

                        <!-- Repair outcome - insertion -->
                        <rect x="400" y="150" width="100" height="15" fill="#27ae60" rx="3"/>
                        <rect x="500" y="150" width="30" height="15" fill="#f39c12" rx="3"/>
                        <rect x="530" y="150" width="120" height="15" fill="#27ae60" rx="3"/>
                        <text x="465" y="162" text-anchor="middle" font-size="8" fill="white">Original</text>
                        <text x="515" y="162" text-anchor="middle" font-size="8" fill="white">Insert</text>
                        <text x="590" y="162" text-anchor="middle" font-size="8" fill="white">Original</text>
                        <text x="525" y="180" text-anchor="middle" font-size="9" fill="#666">Information Gain</text>

                        <!-- Statistics -->
                        <rect x="50" y="220" width="600" height="100" fill="white" stroke="#bdc3c7" rx="8"/>
                        <text x="350" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Editing Statistics Comparison</text>

                        <!-- Cas9 stats -->
                        <text x="200" y="260" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Cas9/CARLIN</text>
                        <text x="200" y="275" text-anchor="middle" font-size="10" fill="#333">2.5 deletions : 1.5 insertions</text>
                        <text x="200" y="290" text-anchor="middle" font-size="10" fill="#333">Median: 163 bp deleted</text>
                        <text x="200" y="305" text-anchor="middle" font-size="10" fill="#333">Median: 2 bp inserted</text>

                        <!-- DARLIN stats -->
                        <text x="500" y="260" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">DARLIN</text>
                        <text x="500" y="275" text-anchor="middle" font-size="10" fill="#333">1+ insertions : 1 deletion</text>
                        <text x="500" y="290" text-anchor="middle" font-size="10" fill="#333">Reduced deletions</text>
                        <text x="500" y="305" text-anchor="middle" font-size="10" fill="#333">Increased insertions</text>
                    </svg>
                </div>

                <h3>How TdT Works</h3>
                <div class="step-box">
                    <span class="step-number">2</span>
                    <strong>TdT Mechanism:</strong> TdT is a template-independent DNA polymerase that adds random nucleotides (A, T, G, C) to both overhang and blunt 3' DNA ends, creating diverse insertion sequences.
                </div>

                <div class="math-formula">
                    <p><strong>Barcode Diversity Formula:</strong></p>
                    <p>Shannon Diversity: $H = 2^{-\sum_{i} p_i \log_2 p_i}$</p>
                    <p>Where $p_i$ is the frequency of allele $i$</p>
                </div>

                <h3>Target Array Design</h3>
                <p>DARLIN uses target arrays containing 10 CRISPR target sites that can be edited simultaneously:</p>

                <div class="svg-container">
                    <svg width="600" height="200" viewBox="0 0 600 200">
                        <rect width="600" height="200" fill="#f8f9fa" rx="10"/>

                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Target Array Structure</text>

                        <!-- Target sites -->
                        <rect x="50" y="50" width="40" height="25" fill="#3498db" stroke="#2980b9" rx="3"/>
                        <text x="70" y="67" text-anchor="middle" font-size="10" fill="white">T1</text>

                        <rect x="100" y="50" width="40" height="25" fill="#3498db" stroke="#2980b9" rx="3"/>
                        <text x="120" y="67" text-anchor="middle" font-size="10" fill="white">T2</text>

                        <rect x="150" y="50" width="40" height="25" fill="#3498db" stroke="#2980b9" rx="3"/>
                        <text x="170" y="67" text-anchor="middle" font-size="10" fill="white">T3</text>

                        <text x="210" y="67" text-anchor="middle" font-size="12" fill="#666">...</text>

                        <rect x="250" y="50" width="40" height="25" fill="#3498db" stroke="#2980b9" rx="3"/>
                        <text x="270" y="67" text-anchor="middle" font-size="10" fill="white">T10</text>

                        <!-- gRNA arrows -->
                        <path d="M 70 40 L 70 50" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        <path d="M 120 40 L 120 50" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        <path d="M 170 40 L 170 50" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        <path d="M 270 40 L 270 50" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead2)"/>

                        <text x="160" y="35" text-anchor="middle" font-size="10" fill="#e74c3c">gRNAs target each site</text>

                        <!-- After editing -->
                        <text x="300" y="110" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">After Cas9-TdT Editing</text>

                        <!-- Edited array -->
                        <rect x="50" y="130" width="35" height="25" fill="#27ae60" rx="3"/>
                        <rect x="85" y="130" width="15" height="25" fill="#f39c12" rx="3"/>
                        <rect x="100" y="130" width="45" height="25" fill="#27ae60" rx="3"/>
                        <rect x="145" y="130" width="20" height="25" fill="#f39c12" rx="3"/>
                        <rect x="165" y="130" width="35" height="25" fill="#27ae60" rx="3"/>
                        <text x="210" y="147" text-anchor="middle" font-size="12" fill="#666">...</text>
                        <rect x="250" y="130" width="40" height="25" fill="#27ae60" rx="3"/>

                        <text x="160" y="175" text-anchor="middle" font-size="10" fill="#27ae60">Green: Original sequences</text>
                        <text x="160" y="190" text-anchor="middle" font-size="10" fill="#f39c12">Orange: TdT insertions</text>

                        <defs>
                            <marker id="arrowhead2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Section 3: System Architecture -->
        <div class="section" id="architecture">
            <div class="section-header">
                🏗️ 3. DARLIN System Architecture
            </div>
            <div class="section-content">
                <h3>Three Independent Target Arrays</h3>
                <p>DARLIN's major architectural innovation is the use of three independent target arrays integrated at different genomic loci:</p>

                <div class="svg-container">
                    <svg width="750" height="400" viewBox="0 0 750 400">
                        <rect width="750" height="400" fill="#f8f9fa" rx="10"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">DARLIN Three-Array Architecture</text>

                        <!-- Mouse genome representation -->
                        <ellipse cx="375" cy="80" rx="150" ry="30" fill="#ecf0f1" stroke="#95a5a6" stroke-width="2"/>
                        <text x="375" y="85" text-anchor="middle" font-size="12" fill="#2c3e50">Mouse Genome</text>

                        <!-- Array 1: Col1a1 (CA) -->
                        <rect x="100" y="130" width="120" height="40" fill="#e74c3c" rx="5"/>
                        <text x="160" y="145" text-anchor="middle" font-size="10" fill="white">Col1a1 Locus</text>
                        <text x="160" y="160" text-anchor="middle" font-size="10" fill="white">CA Array</text>

                        <!-- Array 2: Tigre (TA) -->
                        <rect x="315" y="130" width="120" height="40" fill="#27ae60" rx="5"/>
                        <text x="375" y="145" text-anchor="middle" font-size="10" fill="white">Tigre Locus</text>
                        <text x="375" y="160" text-anchor="middle" font-size="10" fill="white">TA Array</text>

                        <!-- Array 3: Rosa26 (RA) -->
                        <rect x="530" y="130" width="120" height="40" fill="#3498db" rx="5"/>
                        <text x="590" y="145" text-anchor="middle" font-size="10" fill="white">Rosa26 Locus</text>
                        <text x="590" y="160" text-anchor="middle" font-size="10" fill="white">RA Array</text>

                        <!-- Arrows from genome to arrays -->
                        <path d="M 280 100 L 160 130" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 375 110 L 375 130" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 470 100 L 590 130" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>

                        <!-- Target site details -->
                        <text x="375" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Each Array Contains 10 Target Sites</text>

                        <!-- Target sites visualization -->
                        <g transform="translate(200, 220)">
                            <rect x="0" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="35" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="70" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="105" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="140" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="175" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="210" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="245" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="280" y="0" width="30" height="20" fill="#3498db" rx="2"/>
                            <rect x="315" y="0" width="30" height="20" fill="#3498db" rx="2"/>

                            <text x="15" y="13" text-anchor="middle" font-size="8" fill="white">1</text>
                            <text x="50" y="13" text-anchor="middle" font-size="8" fill="white">2</text>
                            <text x="85" y="13" text-anchor="middle" font-size="8" fill="white">3</text>
                            <text x="120" y="13" text-anchor="middle" font-size="8" fill="white">4</text>
                            <text x="155" y="13" text-anchor="middle" font-size="8" fill="white">5</text>
                            <text x="190" y="13" text-anchor="middle" font-size="8" fill="white">6</text>
                            <text x="225" y="13" text-anchor="middle" font-size="8" fill="white">7</text>
                            <text x="260" y="13" text-anchor="middle" font-size="8" fill="white">8</text>
                            <text x="295" y="13" text-anchor="middle" font-size="8" fill="white">9</text>
                            <text x="330" y="13" text-anchor="middle" font-size="8" fill="white">10</text>
                        </g>

                        <!-- Barcode capacity calculation -->
                        <rect x="50" y="270" width="650" height="80" fill="#e8f5e8" stroke="#27ae60" rx="8"/>
                        <text x="375" y="290" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Total Barcode Capacity</text>
                        <text x="375" y="310" text-anchor="middle" font-size="12" fill="#333">CA Array: ~10⁶ alleles</text>
                        <text x="375" y="325" text-anchor="middle" font-size="12" fill="#333">Three Independent Arrays: ~10¹⁸ total combinations</text>
                        <text x="375" y="340" text-anchor="middle" font-size="10" fill="#666">(Exceeds ~10¹⁰ total cells in adult mouse)</text>

                        <defs>
                            <marker id="arrowhead3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Inducible Expression System</h3>
                <p>DARLIN uses a doxycycline (Dox)-inducible system for temporal control of barcode generation:</p>

                <div class="step-box">
                    <span class="step-number">3</span>
                    <strong>Induction Control:</strong> The tetO-Cas9-TdT construct is activated by M2-rtTA in the presence of doxycycline, allowing precise timing of lineage labeling.
                </div>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Component</th>
                            <th>Location</th>
                            <th>Function</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>tetO-Cas9-TdT</strong></td>
                            <td>Col1a1 locus</td>
                            <td>Inducible editing enzyme</td>
                        </tr>
                        <tr>
                            <td><strong>M2-rtTA</strong></td>
                            <td>Rosa26 locus</td>
                            <td>Dox-responsive activator</td>
                        </tr>
                        <tr>
                            <td><strong>gRNAs</strong></td>
                            <td>Col1a1 locus (2 copies)</td>
                            <td>Target site recognition</td>
                        </tr>
                        <tr>
                            <td><strong>CA Array</strong></td>
                            <td>Col1a1 locus</td>
                            <td>Primary target array</td>
                        </tr>
                        <tr>
                            <td><strong>TA Array</strong></td>
                            <td>Tigre locus</td>
                            <td>Secondary target array</td>
                        </tr>
                        <tr>
                            <td><strong>RA Array</strong></td>
                            <td>Rosa26 locus</td>
                            <td>Tertiary target array</td>
                        </tr>
                    </tbody>
                </table>

                <div class="key-point">
                    <strong>🔑 Key Advantage:</strong> Three independent arrays provide redundancy and dramatically increase the total barcode space while maintaining independent editing events.
                </div>
            </div>
        </div>

        <!-- Section 4: Step-by-Step Process -->
        <div class="section" id="process">
            <div class="section-header">
                ⚙️ 4. Step-by-Step DARLIN Process
            </div>
            <div class="section-content">
                <h3>Overview of the DARLIN Workflow</h3>
                <p>The DARLIN process involves several carefully orchestrated steps to generate and analyze lineage barcodes:</p>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <rect width="800" height="500" fill="#f8f9fa" rx="10"/>

                        <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">DARLIN Workflow Timeline</text>

                        <!-- Timeline -->
                        <line x1="100" y1="60" x2="700" y2="60" stroke="#34495e" stroke-width="3"/>

                        <!-- Step 1: Induction -->
                        <circle cx="150" cy="60" r="10" fill="#e74c3c"/>
                        <rect x="100" y="80" width="100" height="80" fill="#ffebee" stroke="#e74c3c" rx="8"/>
                        <text x="150" y="100" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Step 1</text>
                        <text x="150" y="115" text-anchor="middle" font-size="10" fill="#333">Dox Induction</text>
                        <text x="150" y="130" text-anchor="middle" font-size="9" fill="#666">Activate Cas9-TdT</text>
                        <text x="150" y="145" text-anchor="middle" font-size="9" fill="#666">1 week treatment</text>

                        <!-- Step 2: Editing -->
                        <circle cx="300" cy="60" r="10" fill="#f39c12"/>
                        <rect x="250" y="80" width="100" height="80" fill="#fff3cd" stroke="#f39c12" rx="8"/>
                        <text x="300" y="100" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Step 2</text>
                        <text x="300" y="115" text-anchor="middle" font-size="10" fill="#333">Barcode Editing</text>
                        <text x="300" y="130" text-anchor="middle" font-size="9" fill="#666">Cas9-TdT cuts</text>
                        <text x="300" y="145" text-anchor="middle" font-size="9" fill="#666">TdT insertions</text>

                        <!-- Step 3: Development -->
                        <circle cx="450" cy="60" r="10" fill="#3498db"/>
                        <rect x="400" y="80" width="100" height="80" fill="#e3f2fd" stroke="#3498db" rx="8"/>
                        <text x="450" y="100" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Step 3</text>
                        <text x="450" y="115" text-anchor="middle" font-size="10" fill="#333">Development</text>
                        <text x="450" y="130" text-anchor="middle" font-size="9" fill="#666">Cell division</text>
                        <text x="450" y="145" text-anchor="middle" font-size="9" fill="#666">Barcode inheritance</text>

                        <!-- Step 4: Analysis -->
                        <circle cx="600" cy="60" r="10" fill="#27ae60"/>
                        <rect x="550" y="80" width="100" height="80" fill="#e8f5e8" stroke="#27ae60" rx="8"/>
                        <text x="600" y="100" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Step 4</text>
                        <text x="600" y="115" text-anchor="middle" font-size="10" fill="#333">Single-cell Analysis</text>
                        <text x="600" y="130" text-anchor="middle" font-size="9" fill="#666">scRNA-seq</text>
                        <text x="600" y="145" text-anchor="middle" font-size="9" fill="#666">Lineage reconstruction</text>

                        <!-- Arrows -->
                        <path d="M 200 120 L 240 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 350 120 L 390 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 500 120 L 540 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>

                        <!-- Detailed mechanism -->
                        <text x="400" y="200" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Detailed Editing Mechanism</text>

                        <!-- Before editing -->
                        <text x="200" y="230" text-anchor="middle" font-size="12" fill="#333">Before Editing</text>
                        <rect x="150" y="240" width="100" height="15" fill="#3498db" rx="3"/>
                        <text x="200" y="252" text-anchor="middle" font-size="8" fill="white">Intact Target Array</text>

                        <!-- After editing -->
                        <text x="600" y="230" text-anchor="middle" font-size="12" fill="#333">After Editing</text>
                        <rect x="520" y="240" width="40" height="15" fill="#27ae60" rx="2"/>
                        <rect x="560" y="240" width="20" height="15" fill="#f39c12" rx="2"/>
                        <rect x="580" y="240" width="30" height="15" fill="#27ae60" rx="2"/>
                        <rect x="610" y="240" width="15" height="15" fill="#f39c12" rx="2"/>
                        <rect x="625" y="240" width="35" height="15" fill="#27ae60" rx="2"/>

                        <!-- Arrow between -->
                        <path d="M 260 247 L 510 247" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead4)"/>
                        <text x="385" y="242" text-anchor="middle" font-size="10" fill="#e74c3c">Cas9-TdT</text>

                        <!-- Legend -->
                        <rect x="50" y="300" width="700" height="120" fill="white" stroke="#bdc3c7" rx="8"/>
                        <text x="400" y="320" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Features</text>

                        <circle cx="120" cy="340" r="5" fill="#e74c3c"/>
                        <text x="140" y="345" font-size="11" fill="#333">Inducible at any developmental stage</text>

                        <circle cx="120" cy="360" r="5" fill="#f39c12"/>
                        <text x="140" y="365" font-size="11" fill="#333">Works across all tissues</text>

                        <circle cx="120" cy="380" r="5" fill="#3498db"/>
                        <text x="140" y="385" font-size="11" fill="#333">Transcriptionally active barcodes</text>

                        <circle cx="420" cy="340" r="5" fill="#27ae60"/>
                        <text x="440" y="345" font-size="11" fill="#333">~90% editing efficiency</text>

                        <circle cx="420" cy="360" r="5" fill="#9b59b6"/>
                        <text x="440" y="365" font-size="11" fill="#333">~60% single-cell coverage</text>

                        <circle cx="420" cy="380" r="5" fill="#34495e"/>
                        <text x="440" y="385" font-size="11" fill="#333">Compatible with multi-omics</text>

                        <defs>
                            <marker id="arrowhead4" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Detailed Step-by-Step Process</h3>

                <div class="step-box">
                    <span class="step-number">1</span>
                    <strong>Doxycycline Induction</strong>
                    <p>Administer doxycycline to DARLIN mice to activate the tetO-Cas9-TdT system. This can be done at any developmental stage:</p>
                    <ul>
                        <li><strong>E10.0:</strong> Early embryonic development</li>
                        <li><strong>E17.0:</strong> Late fetal development</li>
                        <li><strong>Neonate:</strong> Shortly after birth</li>
                        <li><strong>Adult:</strong> Mature tissue homeostasis</li>
                    </ul>
                    <p><em>Typical protocol:</em> 1 week of doxycycline treatment followed by 3 days washout</p>
                </div>

                <div class="step-box">
                    <span class="step-number">2</span>
                    <strong>Cas9-TdT Editing</strong>
                    <p>The activated Cas9-TdT fusion protein targets the 10 CRISPR sites in each of the three arrays:</p>
                    <ul>
                        <li>Cas9 creates double-strand breaks at target sites</li>
                        <li>TdT adds random nucleotides during repair</li>
                        <li>Each cell receives a unique combination of edits</li>
                        <li>Editing is independent across the three arrays</li>
                    </ul>
                </div>

                <div class="step-box">
                    <span class="step-number">3</span>
                    <strong>Barcode Inheritance</strong>
                    <p>Edited barcodes are stably inherited through cell divisions:</p>
                    <ul>
                        <li>Each daughter cell carries the same barcode as its parent</li>
                        <li>Clonally related cells share identical barcodes</li>
                        <li>Barcodes are transcriptionally active</li>
                    </ul>
                </div>

                <div class="step-box">
                    <span class="step-number">4</span>
                    <strong>Single-Cell Analysis</strong>
                    <p>Harvest tissues and perform single-cell RNA sequencing:</p>
                    <ul>
                        <li>Isolate single cells from target tissues</li>
                        <li>Capture both endogenous mRNA and barcode transcripts</li>
                        <li>Sequence and analyze lineage relationships</li>
                        <li>Integrate with transcriptomic data</li>
                    </ul>
                </div>

                <div class="math-formula">
                    <p><strong>Quality Control Metrics:</strong></p>
                    <p>Rare Allele Threshold: $P_{homoplasy} < 0.05$</p>
                    <p>Where $P_{homoplasy}$ is the probability of independent occurrence</p>
                </div>
            </div>
        </div>

        <!-- Section 5: Applications -->
        <div class="section" id="applications">
            <div class="section-header">
                🧪 5. Applications and Results
            </div>
            <div class="section-content">
                <h3>Hematopoietic Stem Cell Fate Mapping</h3>
                <p>DARLIN enabled unprecedented resolution in studying blood cell development and fate decisions:</p>

                <div class="svg-container">
                    <svg width="700" height="400" viewBox="0 0 700 400">
                        <rect width="700" height="400" fill="#f8f9fa" rx="10"/>

                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Hematopoietic Lineage Tree Revealed by DARLIN</text>

                        <!-- HSC at top -->
                        <circle cx="350" cy="70" r="20" fill="#e74c3c"/>
                        <text x="350" y="76" text-anchor="middle" font-size="10" fill="white">HSC</text>

                        <!-- LMPP branch -->
                        <circle cx="250" cy="150" r="15" fill="#f39c12"/>
                        <text x="250" y="156" text-anchor="middle" font-size="9" fill="white">LMPP</text>

                        <!-- Megakaryocyte direct path -->
                        <circle cx="450" cy="150" r="15" fill="#9b59b6"/>
                        <text x="450" y="156" text-anchor="middle" font-size="9" fill="white">Meg</text>

                        <!-- Mature cell types -->
                        <circle cx="150" cy="230" r="12" fill="#27ae60"/>
                        <text x="150" y="236" text-anchor="middle" font-size="8" fill="white">Mon</text>

                        <circle cx="250" cy="230" r="12" fill="#3498db"/>
                        <text x="250" y="236" text-anchor="middle" font-size="8" fill="white">Neu</text>

                        <circle cx="350" cy="230" r="12" fill="#e67e22"/>
                        <text x="350" y="236" text-anchor="middle" font-size="8" fill="white">Ery</text>

                        <circle cx="450" cy="230" r="12" fill="#9b59b6"/>
                        <text x="450" y="236" text-anchor="middle" font-size="8" fill="white">Meg</text>

                        <!-- Connections -->
                        <path d="M 335 85 L 265 135" stroke="#666" stroke-width="2"/>
                        <path d="M 365 85 L 435 135" stroke="#e74c3c" stroke-width="3"/>
                        <text x="400" y="115" font-size="9" fill="#e74c3c">Direct HSC→Meg</text>

                        <path d="M 240 165 L 160 215" stroke="#666" stroke-width="2"/>
                        <path d="M 250 165 L 250 215" stroke="#666" stroke-width="2"/>
                        <path d="M 260 165 L 340 215" stroke="#666" stroke-width="2"/>
                        <path d="M 450 165 L 450 215" stroke="#9b59b6" stroke-width="2"/>

                        <!-- Key findings box -->
                        <rect x="50" y="280" width="600" height="100" fill="white" stroke="#27ae60" stroke-width="2" rx="8"/>
                        <text x="350" y="300" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Key Discoveries</text>

                        <text x="70" y="320" font-size="11" fill="#333">• Strong lineage coupling between HSC and Megakaryocytes</text>
                        <text x="70" y="335" font-size="11" fill="#333">• LMPP preferentially generates Monocytes</text>
                        <text x="70" y="350" font-size="11" fill="#333">• 48% of clones showed single-fate bias</text>
                        <text x="70" y="365" font-size="11" fill="#333">• Early fate priming detected in HSCs</text>

                        <!-- Legend -->
                        <text x="350" y="395" text-anchor="middle" font-size="10" fill="#666">HSC: Hematopoietic Stem Cell, LMPP: Lymphoid-biased Multipotent Progenitor</text>
                        <text x="350" y="408" text-anchor="middle" font-size="10" fill="#666">Mon: Monocyte, Neu: Neutrophil, Ery: Erythrocyte, Meg: Megakaryocyte</text>
                    </svg>
                </div>

                <h3>Cell Migration Studies</h3>
                <p>DARLIN revealed novel insights into hematopoietic stem cell migration patterns:</p>

                <div class="highlight-box">
                    <strong>🔍 Migration Findings:</strong>
                    <ul>
                        <li><strong>Adult HSCs:</strong> ~5% shared clone fraction between bones (physiological circulation)</li>
                        <li><strong>MPPs:</strong> ~14% inter-bone sharing (more active circulation)</li>
                        <li><strong>Myeloid Progenitors:</strong> ~40% sharing (highest circulation)</li>
                        <li><strong>Developmental timing:</strong> E17.0 labeling shows predominantly local hematopoiesis</li>
                    </ul>
                </div>

                <h3>Camellia-seq: Multi-omics Integration</h3>
                <p>DARLIN enabled the development of Camellia-seq, which simultaneously measures:</p>

                <div class="svg-container">
                    <svg width="600" height="300" viewBox="0 0 600 300">
                        <rect width="600" height="300" fill="#f8f9fa" rx="10"/>

                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Camellia-seq Multi-omics Approach</text>

                        <!-- Single cell -->
                        <circle cx="300" cy="70" r="25" fill="#3498db"/>
                        <text x="300" y="76" text-anchor="middle" font-size="12" fill="white">Single Cell</text>

                        <!-- Split into fractions -->
                        <rect x="150" y="120" width="80" height="40" fill="#e74c3c" rx="5"/>
                        <text x="190" y="135" text-anchor="middle" font-size="10" fill="white">Cytoplasmic</text>
                        <text x="190" y="150" text-anchor="middle" font-size="10" fill="white">Fraction</text>

                        <rect x="370" y="120" width="80" height="40" fill="#27ae60" rx="5"/>
                        <text x="410" y="135" text-anchor="middle" font-size="10" fill="white">Nuclear</text>
                        <text x="410" y="150" text-anchor="middle" font-size="10" fill="white">Fraction</text>

                        <!-- Arrows from cell -->
                        <path d="M 280 85 L 210 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 320 85 L 390 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead5)"/>

                        <!-- Cytoplasmic analysis -->
                        <rect x="100" y="180" width="180" height="60" fill="#ffebee" stroke="#e74c3c" rx="5"/>
                        <text x="190" y="200" text-anchor="middle" font-size="11" font-weight="bold" fill="#e74c3c">RNA Analysis</text>
                        <text x="190" y="215" text-anchor="middle" font-size="9" fill="#333">• Gene expression</text>
                        <text x="190" y="228" text-anchor="middle" font-size="9" fill="#333">• Lineage barcodes</text>

                        <!-- Nuclear analysis -->
                        <rect x="320" y="180" width="180" height="60" fill="#e8f5e8" stroke="#27ae60" rx="5"/>
                        <text x="410" y="200" text-anchor="middle" font-size="11" font-weight="bold" fill="#27ae60">DNA Analysis</text>
                        <text x="410" y="215" text-anchor="middle" font-size="9" fill="#333">• DNA methylation (CpG)</text>
                        <text x="410" y="228" text-anchor="middle" font-size="9" fill="#333">• Chromatin accessibility (GpC)</text>

                        <!-- Arrows to analysis -->
                        <path d="M 190 160 L 190 180" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 410 160 L 410 180" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead5)"/>

                        <defs>
                            <marker id="arrowhead5" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <div class="key-point">
                    <strong>🎯 Camellia-seq Achievement:</strong> First method to simultaneously profile lineage barcodes, gene expression, DNA methylation, and chromatin accessibility in single cells.
                </div>
            </div>
        </div>

        <!-- Section 6: Advantages and Limitations -->
        <div class="section" id="advantages">
            <div class="section-header">
                ⚖️ 6. Advantages and Limitations
            </div>
            <div class="section-content">
                <h3>Major Advantages of DARLIN</h3>

                <div class="svg-container">
                    <svg width="700" height="350" viewBox="0 0 700 350">
                        <rect width="700" height="350" fill="#f8f9fa" rx="10"/>

                        <text x="350" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">DARLIN Advantages</text>

                        <!-- Advantage 1: High Coverage -->
                        <rect x="50" y="50" width="150" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="8"/>
                        <text x="125" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">High Coverage</text>
                        <text x="125" y="85" text-anchor="middle" font-size="10" fill="#333">60% cell coverage</text>
                        <text x="125" y="100" text-anchor="middle" font-size="10" fill="#333">vs 10% in CARLIN</text>
                        <text x="125" y="115" text-anchor="middle" font-size="9" fill="#666">6x improvement</text>

                        <!-- Advantage 2: Massive Diversity -->
                        <rect x="275" y="50" width="150" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="2" rx="8"/>
                        <text x="350" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Massive Diversity</text>
                        <text x="350" y="85" text-anchor="middle" font-size="10" fill="#333">~10¹⁸ barcodes</text>
                        <text x="350" y="100" text-anchor="middle" font-size="10" fill="#333">vs 44K in CARLIN</text>
                        <text x="350" y="115" text-anchor="middle" font-size="9" fill="#666">10¹⁴x improvement</text>

                        <!-- Advantage 3: Multi-omics -->
                        <rect x="500" y="50" width="150" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="8"/>
                        <text x="575" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Multi-omics</text>
                        <text x="575" y="85" text-anchor="middle" font-size="10" fill="#333">4 modalities</text>
                        <text x="575" y="100" text-anchor="middle" font-size="10" fill="#333">simultaneously</text>
                        <text x="575" y="115" text-anchor="middle" font-size="9" fill="#666">First of its kind</text>

                        <!-- Performance metrics -->
                        <rect x="50" y="160" width="600" height="120" fill="white" stroke="#bdc3c7" rx="8"/>
                        <text x="350" y="180" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Performance Metrics</text>

                        <!-- Metrics grid -->
                        <text x="100" y="200" font-size="11" font-weight="bold" fill="#333">Editing Efficiency:</text>
                        <text x="250" y="200" font-size="11" fill="#27ae60">>90% (vs 30-50%)</text>

                        <text x="100" y="220" font-size="11" font-weight="bold" fill="#333">Singleton Fraction:</text>
                        <text x="250" y="220" font-size="11" fill="#27ae60">~65% (vs 30%)</text>

                        <text x="100" y="240" font-size="11" font-weight="bold" fill="#333">Insertion:Deletion Ratio:</text>
                        <text x="250" y="240" font-size="11" fill="#27ae60">1+:1 (vs 1.5:2.5)</text>

                        <text x="400" y="200" font-size="11" font-weight="bold" fill="#333">Tissue Coverage:</text>
                        <text x="550" y="200" font-size="11" fill="#27ae60">All tested tissues</text>

                        <text x="400" y="220" font-size="11" font-weight="bold" fill="#333">Temporal Control:</text>
                        <text x="550" y="220" font-size="11" fill="#27ae60">Any developmental stage</text>

                        <text x="400" y="240" font-size="11" font-weight="bold" fill="#333">Genomic Coverage:</text>
                        <text x="550" y="240" font-size="11" fill="#27ae60">70% promoters, 90% genes</text>

                        <!-- Cost effectiveness -->
                        <rect x="150" y="300" width="400" height="30" fill="#d4edda" stroke="#27ae60" rx="5"/>
                        <text x="350" y="320" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Dramatically Reduced Experimental Costs</text>
                    </svg>
                </div>

                <h3>Current Limitations</h3>
                <p>Despite its significant improvements, DARLIN still has some limitations:</p>

                <div class="step-box" style="border-left-color: #e74c3c; background: #ffebee;">
                    <span class="step-number" style="background: #e74c3c;">!</span>
                    <strong>Array Deletions:</strong> Target arrays can still suffer from large deletions that limit lineage reconstruction across multiple cell divisions.
                </div>

                <div class="step-box" style="border-left-color: #f39c12; background: #fff3cd;">
                    <span class="step-number" style="background: #f39c12;">!</span>
                    <strong>Camellia-seq Throughput:</strong> Currently a low-throughput, plate-based method requiring deep genomic coverage per cell.
                </div>

                <h3>Future Directions</h3>
                <ul>
                    <li><strong>Sequential Recording:</strong> Implementing hierarchical barcode systems for multi-generational lineage tracking</li>
                    <li><strong>Spatial Integration:</strong> Combining DARLIN with spatial transcriptomics</li>
                    <li><strong>High-throughput Multi-omics:</strong> Developing cost-effective, scalable Camellia-seq protocols</li>
                    <li><strong>Cross-species Applications:</strong> Adapting DARLIN for other model organisms</li>
                </ul>

                <div class="highlight-box">
                    <strong>🚀 Impact:</strong> DARLIN represents a paradigm shift in lineage tracing, enabling studies of large biological systems like adult tissue homeostasis, inflammation response, and tissue injury repair at unprecedented resolution.
                </div>

                <h3>Summary</h3>
                <p>DARLIN is a transformative technology that:</p>
                <ul>
                    <li>Provides 6x better single-cell lineage coverage than previous systems</li>
                    <li>Generates >10¹⁴ times more barcode diversity</li>
                    <li>Enables multi-omics integration with lineage information</li>
                    <li>Works across all tissues and developmental stages</li>
                    <li>Opens new avenues for understanding development, homeostasis, and disease</li>
                </ul>

                <div class="math-formula">
                    <p><strong>DARLIN Performance Summary:</strong></p>
                    <p>Cell Coverage: $\frac{60\%}{10\%} = 6\times$ improvement</p>
                    <p>Barcode Diversity: $\frac{10^{18}}{4.4 \times 10^4} \approx 2.3 \times 10^{13}\times$ improvement</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding: 40px; color: #666;">
            <p>Based on: Li et al. (2023) "A mouse model with high clonal barcode diversity for joint lineage, transcriptomic, and epigenomic profiling in single cells"</p>
            <p style="margin-top: 10px; font-size: 0.9em;">Tutorial created with advanced HTML, CSS, and SVG visualizations</p>
        </div>
    </div>
</body>
</html>
