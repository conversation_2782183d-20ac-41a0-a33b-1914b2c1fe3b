<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DARLIN Snakemake Pipeline - Complete Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #9b59b6;
            --dark-color: #34495e;
            --light-bg: #ecf0f1;
            --text-color: #2c3e50;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1300px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
            color: white;
            padding: 50px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
        }

        .header p {
            font-size: 1.4em;
            opacity: 0.9;
        }

        .outline {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
            border-left: 8px solid var(--info-color);
        }

        .outline h2 {
            color: var(--primary-color);
            margin-bottom: 25px;
            font-size: 2.2em;
        }

        .outline ol {
            padding-left: 30px;
        }

        .outline li {
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .outline li ul {
            margin-top: 10px;
            padding-left: 30px;
        }

        .outline li ul li {
            font-size: 1em;
            color: #666;
            margin-bottom: 8px;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border-left: 8px solid var(--accent-color);
        }

        .section-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
            padding: 30px 40px;
            font-size: 1.8em;
            font-weight: bold;
        }

        .section-content {
            padding: 40px;
        }

        .pipeline-step {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 6px solid var(--success-color);
            padding: 25px;
            margin: 25px 0;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .step-number {
            background: var(--success-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            font-size: 1.2em;
        }

        .config-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid var(--warning-color);
            padding: 25px;
            margin: 25px 0;
            border-radius: 12px;
            border-left: 6px solid var(--warning-color);
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 25px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            overflow-x: auto;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .command-block {
            background: #34495e;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            border-left: 4px solid var(--secondary-color);
        }

        .pipeline-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .pipeline-table th {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: left;
            font-size: 1.1em;
        }

        .pipeline-table td {
            padding: 18px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .pipeline-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .svg-container {
            text-align: center;
            margin: 35px 0;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .key-feature {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left: 6px solid var(--success-color);
            padding: 25px;
            margin: 25px 0;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .warning-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-left: 6px solid var(--accent-color);
            padding: 25px;
            margin: 25px 0;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 250px;
            z-index: 1000;
        }

        .navigation h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .navigation ul {
            list-style: none;
        }

        .navigation li {
            margin-bottom: 10px;
        }

        .navigation a {
            color: var(--secondary-color);
            text-decoration: none;
            font-size: 1em;
            display: block;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .navigation a:hover {
            background: var(--light-bg);
            transform: translateX(5px);
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <h3>Pipeline Navigation</h3>
        <ul>
            <li><a href="#overview">Pipeline Overview</a></li>
            <li><a href="#installation">Installation</a></li>
            <li><a href="#configuration">Configuration</a></li>
            <li><a href="#workflow">Workflow Steps</a></li>
            <li><a href="#protocols">Protocol Types</a></li>
            <li><a href="#analysis">Analysis Methods</a></li>
            <li><a href="#outputs">Outputs & QC</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>DARLIN Snakemake Pipeline</h1>
            <p>Automated Processing for High-Resolution Lineage Tracing Data</p>
        </div>

        <div class="outline">
            <h2>🐍 Pipeline Tutorial Outline</h2>
            <ol>
                <li><strong>Pipeline Overview and Architecture</strong>
                    <ul>
                        <li>Snakemake workflow management system</li>
                        <li>Integration with Custom CARLIN pipeline</li>
                        <li>Multi-protocol support (bulk, single-cell)</li>
                        <li>HPC and local execution modes</li>
                    </ul>
                </li>
                <li><strong>Installation and Dependencies</strong>
                    <ul>
                        <li>Conda environment setup</li>
                        <li>Required software dependencies</li>
                        <li>MATLAB and bioinformatics tools</li>
                        <li>HPC-specific configurations</li>
                    </ul>
                </li>
                <li><strong>Configuration Management</strong>
                    <ul>
                        <li>config.yaml structure and parameters</li>
                        <li>Protocol-specific settings</li>
                        <li>Template and primer set selection</li>
                        <li>Resource allocation strategies</li>
                    </ul>
                </li>
                <li><strong>Workflow Steps and Execution</strong>
                    <ul>
                        <li>Data acquisition from BaseSpace</li>
                        <li>Part 1: Preprocessing and CARLIN analysis</li>
                        <li>Part 2: Aggregation and reporting</li>
                        <li>Quality control integration</li>
                    </ul>
                </li>
                <li><strong>Protocol Types and Applications</strong>
                    <ul>
                        <li>Bulk RNA protocols (Tigre, Rosa, Col1a1)</li>
                        <li>Single-cell protocols (10X, Camellia-seq)</li>
                        <li>UMI and cell barcode handling</li>
                        <li>Template-specific processing</li>
                    </ul>
                </li>
                <li><strong>Analysis Methods and Algorithms</strong>
                    <ul>
                        <li>PEAR read merging strategy</li>
                        <li>Quality control metrics</li>
                        <li>Allele frequency analysis</li>
                        <li>Statistical reporting methods</li>
                    </ul>
                </li>
                <li><strong>Outputs and Quality Control</strong>
                    <ul>
                        <li>Generated file structure</li>
                        <li>HTML reporting system</li>
                        <li>Visualization outputs</li>
                        <li>Validation and testing</li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 1: Pipeline Overview -->
        <div class="section" id="overview">
            <div class="section-header">
                🏗️ 1. Pipeline Overview and Architecture
            </div>
            <div class="section-content">
                <h3>What is the DARLIN Snakemake Pipeline?</h3>
                <p>The DARLIN Snakemake pipeline is an automated workflow management system designed to process DARLIN lineage tracing sequencing data. It integrates multiple bioinformatics tools and provides a standardized, reproducible approach to analyzing both bulk and single-cell DARLIN experiments.</p>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <rect width="800" height="500" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#2c3e50">DARLIN Pipeline Architecture</text>

                        <!-- Input layer -->
                        <rect x="50" y="70" width="700" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="400" y="95" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Input Layer</text>

                        <!-- Raw data types -->
                        <rect x="80" y="110" width="120" height="30" fill="#3498db" rx="5"/>
                        <text x="140" y="130" text-anchor="middle" font-size="10" fill="white">Raw FASTQ</text>

                        <rect x="220" y="110" width="120" height="30" fill="#3498db" rx="5"/>
                        <text x="280" y="130" text-anchor="middle" font-size="10" fill="white">Config YAML</text>

                        <rect x="360" y="110" width="120" height="30" fill="#3498db" rx="5"/>
                        <text x="420" y="130" text-anchor="middle" font-size="10" fill="white">Sample Info</text>

                        <rect x="500" y="110" width="120" height="30" fill="#3498db" rx="5"/>
                        <text x="560" y="130" text-anchor="middle" font-size="10" fill="white">Reference</text>

                        <rect x="640" y="110" width="120" height="30" fill="#3498db" rx="5"/>
                        <text x="700" y="130" text-anchor="middle" font-size="10" fill="white">Templates</text>

                        <!-- Processing layer -->
                        <rect x="50" y="180" width="700" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="400" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Processing Layer (Snakemake)</text>

                        <!-- Processing steps -->
                        <rect x="80" y="230" width="100" height="60" fill="#27ae60" rx="8"/>
                        <text x="130" y="250" text-anchor="middle" font-size="10" fill="white">Data</text>
                        <text x="130" y="265" text-anchor="middle" font-size="10" fill="white">Acquisition</text>
                        <text x="130" y="280" text-anchor="middle" font-size="9" fill="white">BaseSpace</text>

                        <rect x="200" y="230" width="100" height="60" fill="#27ae60" rx="8"/>
                        <text x="250" y="250" text-anchor="middle" font-size="10" fill="white">PEAR</text>
                        <text x="250" y="265" text-anchor="middle" font-size="10" fill="white">Merging</text>
                        <text x="250" y="280" text-anchor="middle" font-size="9" fill="white">R1+R2</text>

                        <rect x="320" y="230" width="100" height="60" fill="#27ae60" rx="8"/>
                        <text x="370" y="250" text-anchor="middle" font-size="10" fill="white">Quality</text>
                        <text x="370" y="265" text-anchor="middle" font-size="10" fill="white">Control</text>
                        <text x="370" y="280" text-anchor="middle" font-size="9" fill="white">FastQC</text>

                        <rect x="440" y="230" width="100" height="60" fill="#27ae60" rx="8"/>
                        <text x="490" y="250" text-anchor="middle" font-size="10" fill="white">CARLIN</text>
                        <text x="490" y="265" text-anchor="middle" font-size="10" fill="white">Analysis</text>
                        <text x="490" y="280" text-anchor="middle" font-size="9" fill="white">MATLAB</text>

                        <rect x="560" y="230" width="100" height="60" fill="#27ae60" rx="8"/>
                        <text x="610" y="250" text-anchor="middle" font-size="10" fill="white">Aggregation</text>
                        <text x="610" y="265" text-anchor="middle" font-size="10" fill="white">& Reporting</text>
                        <text x="610" y="280" text-anchor="middle" font-size="9" fill="white">Python</text>

                        <!-- Output layer -->
                        <rect x="50" y="330" width="700" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="400" y="355" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Output Layer</text>

                        <!-- Output types -->
                        <rect x="80" y="370" width="120" height="30" fill="#f39c12" rx="5"/>
                        <text x="140" y="390" text-anchor="middle" font-size="10" fill="white">Allele Data</text>

                        <rect x="220" y="370" width="120" height="30" fill="#f39c12" rx="5"/>
                        <text x="280" y="390" text-anchor="middle" font-size="10" fill="white">Statistics</text>

                        <rect x="360" y="370" width="120" height="30" fill="#f39c12" rx="5"/>
                        <text x="420" y="390" text-anchor="middle" font-size="10" fill="white">QC Reports</text>

                        <rect x="500" y="370" width="120" height="30" fill="#f39c12" rx="5"/>
                        <text x="560" y="390" text-anchor="middle" font-size="10" fill="white">Visualizations</text>

                        <rect x="640" y="370" width="120" height="30" fill="#f39c12" rx="5"/>
                        <text x="700" y="390" text-anchor="middle" font-size="10" fill="white">HTML Reports</text>

                        <!-- Arrows -->
                        <path d="M 400 160 L 400 180" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 400 310 L 400 330" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>

                        <!-- Key features -->
                        <rect x="100" y="440" width="600" height="50" fill="white" stroke="#9b59b6" stroke-width="2" rx="8"/>
                        <text x="400" y="460" text-anchor="middle" font-size="14" font-weight="bold" fill="#9b59b6">Key Pipeline Features</text>
                        <text x="200" y="478" text-anchor="middle" font-size="10" fill="#333">Automated</text>
                        <text x="300" y="478" text-anchor="middle" font-size="10" fill="#333">Scalable</text>
                        <text x="400" y="478" text-anchor="middle" font-size="10" fill="#333">Reproducible</text>
                        <text x="500" y="478" text-anchor="middle" font-size="10" fill="#333">Multi-protocol</text>
                        <text x="600" y="478" text-anchor="middle" font-size="10" fill="#333">HPC-ready</text>

                        <defs>
                            <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Core Components</h3>
                <div class="key-feature">
                    <strong>🔧 Snakemake Workflow Manager:</strong> Provides dependency management, parallel execution, and automatic job scheduling for complex bioinformatics pipelines.
                </div>

                <div class="key-feature">
                    <strong>🧬 Custom CARLIN Integration:</strong> Adapted version of the original CARLIN pipeline specifically designed to handle DARLIN's three target arrays (CA, TA, RA).
                </div>

                <div class="key-feature">
                    <strong>📊 Automated Quality Control:</strong> Integrated FastQC, MultiQC, and custom QC metrics to ensure data quality throughout the pipeline.
                </div>

                <h3>Pipeline Advantages</h3>
                <table class="pipeline-table">
                    <thead>
                        <tr>
                            <th>Feature</th>
                            <th>Benefit</th>
                            <th>Implementation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Automation</strong></td>
                            <td>Reduces manual errors and processing time</td>
                            <td>Snakemake rule-based execution</td>
                        </tr>
                        <tr>
                            <td><strong>Scalability</strong></td>
                            <td>Handles multiple samples simultaneously</td>
                            <td>Parallel processing with resource management</td>
                        </tr>
                        <tr>
                            <td><strong>Reproducibility</strong></td>
                            <td>Consistent results across runs</td>
                            <td>Version-controlled configuration files</td>
                        </tr>
                        <tr>
                            <td><strong>Flexibility</strong></td>
                            <td>Supports multiple experimental protocols</td>
                            <td>Protocol-specific configuration templates</td>
                        </tr>
                        <tr>
                            <td><strong>HPC Integration</strong></td>
                            <td>Efficient use of computational resources</td>
                            <td>SLURM job submission and memory management</td>
                        </tr>
                    </tbody>
                </table>

                <div class="warning-box">
                    <strong>⚠️ Important Note:</strong> This pipeline requires the Custom CARLIN software specifically adapted for DARLIN, not the original CARLIN pipeline. The custom version handles the three independent target arrays (CA, TA, RA) that are unique to the DARLIN system.
                </div>
            </div>
        </div>

        <!-- Section 2: Installation -->
        <div class="section" id="installation">
            <div class="section-header">
                ⚙️ 2. Installation and Dependencies
            </div>
            <div class="section-content">
                <h3>Step-by-Step Installation Process</h3>
                <p>The DARLIN pipeline requires careful setup of multiple software dependencies and custom tools:</p>

                <div class="svg-container">
                    <svg width="750" height="400" viewBox="0 0 750 400">
                        <rect width="750" height="400" fill="#f8f9fa" rx="10"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Installation Workflow</text>

                        <!-- Step 1: Conda Environment -->
                        <rect x="50" y="60" width="140" height="80" fill="#3498db" rx="8"/>
                        <text x="120" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Step 1</text>
                        <text x="120" y="95" text-anchor="middle" font-size="10" fill="white">Conda Environment</text>
                        <text x="120" y="110" text-anchor="middle" font-size="9" fill="white">• Python 3.9</text>
                        <text x="120" y="125" text-anchor="middle" font-size="9" fill="white">• Snakemake 7.24.0</text>

                        <!-- Step 2: Python Packages -->
                        <rect x="210" y="60" width="140" height="80" fill="#27ae60" rx="8"/>
                        <text x="280" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Step 2</text>
                        <text x="280" y="95" text-anchor="middle" font-size="10" fill="white">Python Packages</text>
                        <text x="280" y="110" text-anchor="middle" font-size="9" fill="white">• JupyterLab</text>
                        <text x="280" y="125" text-anchor="middle" font-size="9" fill="white">• BioPython</text>

                        <!-- Step 3: DARLIN Code -->
                        <rect x="370" y="60" width="140" height="80" fill="#f39c12" rx="8"/>
                        <text x="440" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Step 3</text>
                        <text x="440" y="95" text-anchor="middle" font-size="10" fill="white">DARLIN Code</text>
                        <text x="440" y="110" text-anchor="middle" font-size="9" fill="white">• Git clone</text>
                        <text x="440" y="125" text-anchor="middle" font-size="9" fill="white">• Setup.py install</text>

                        <!-- Step 4: External Tools -->
                        <rect x="530" y="60" width="140" height="80" fill="#9b59b6" rx="8"/>
                        <text x="600" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Step 4</text>
                        <text x="600" y="95" text-anchor="middle" font-size="10" fill="white">External Tools</text>
                        <text x="600" y="110" text-anchor="middle" font-size="9" fill="white">• PEAR</text>
                        <text x="600" y="125" text-anchor="middle" font-size="9" fill="white">• MATLAB</text>

                        <!-- Arrows -->
                        <path d="M 190 100 L 200 100" stroke="#666" stroke-width="2" marker-end="url(#arrowhead1)"/>
                        <path d="M 350 100 L 360 100" stroke="#666" stroke-width="2" marker-end="url(#arrowhead1)"/>
                        <path d="M 510 100 L 520 100" stroke="#666" stroke-width="2" marker-end="url(#arrowhead1)"/>

                        <!-- Dependencies detail -->
                        <rect x="50" y="170" width="650" height="200" fill="white" stroke="#bdc3c7" rx="10"/>
                        <text x="375" y="190" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Detailed Dependencies</text>

                        <!-- Core dependencies -->
                        <text x="80" y="215" font-size="12" font-weight="bold" fill="#3498db">Core Environment:</text>
                        <text x="100" y="235" font-size="10" fill="#333">• Python 3.9 (conda environment)</text>
                        <text x="100" y="250" font-size="10" fill="#333">• Snakemake 7.24.0 (workflow management)</text>
                        <text x="100" y="265" font-size="10" fill="#333">• Mamba (fast package manager)</text>

                        <!-- Python packages -->
                        <text x="400" y="215" font-size="12" font-weight="bold" fill="#27ae60">Python Packages:</text>
                        <text x="420" y="235" font-size="10" fill="#333">• JupyterLab (notebook interface)</text>
                        <text x="420" y="250" font-size="10" fill="#333">• UMI_tools (UMI processing)</text>
                        <text x="420" y="265" font-size="10" fill="#333">• Seaborn (visualization)</text>
                        <text x="420" y="280" font-size="10" fill="#333">• Papermill (notebook execution)</text>
                        <text x="420" y="295" font-size="10" fill="#333">• BioPython (sequence analysis)</text>

                        <!-- External tools -->
                        <text x="80" y="315" font-size="12" font-weight="bold" fill="#f39c12">External Tools:</text>
                        <text x="100" y="335" font-size="10" fill="#333">• PEAR (paired-end read merger)</text>
                        <text x="100" y="350" font-size="10" fill="#333">• MATLAB (with Bioinformatics Toolbox)</text>

                        <!-- Optional tools -->
                        <text x="400" y="315" font-size="12" font-weight="bold" fill="#9b59b6">Optional QC Tools:</text>
                        <text x="420" y="335" font-size="10" fill="#333">• FastQC (sequence quality assessment)</text>
                        <text x="420" y="350" font-size="10" fill="#333">• MultiQC (aggregate QC reporting)</text>

                        <defs>
                            <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Installation Commands</h3>

                <div class="pipeline-step">
                    <span class="step-number">1</span>
                    <strong>Create Conda Environment</strong>
                    <div class="command-block">
kernel_name='snakemake_darlin'
conda create -n $kernel_name python=3.9 --yes
conda activate $kernel_name
conda install -c conda-forge mamba --yes
mamba install -c conda-forge -c bioconda snakemake=7.24.0 --yes
                    </div>
                </div>

                <div class="pipeline-step">
                    <span class="step-number">2</span>
                    <strong>Install Python Dependencies</strong>
                    <div class="command-block">
pip install --user ipykernel
pip install jupyterlab umi_tools seaborn papermill biopython
python -m ipykernel install --user --name=$kernel_name
                    </div>
                </div>

                <div class="pipeline-step">
                    <span class="step-number">3</span>
                    <strong>Clone and Install DARLIN Pipeline</strong>
                    <div class="command-block">
code_directory='.'  # Change to your desired directory
cd $code_directory

git clone https://github.com/ShouWenWang-Lab/snakemake_DARLIN --depth=1
cd snakemake_DARLIN
python setup.py develop
cd ..
                    </div>
                </div>

                <div class="pipeline-step">
                    <span class="step-number">4</span>
                    <strong>Install Custom CARLIN Pipeline</strong>
                    <div class="command-block">
mkdir CARLIN_pipeline
cd CARLIN_pipeline
git clone https://github.com/ShouWenWang-Lab/Custom_CARLIN --depth=1
                    </div>
                </div>

                <div class="pipeline-step">
                    <span class="step-number">5</span>
                    <strong>Install PEAR (Local HPC Installation)</strong>
                    <div class="command-block">
cd pear_installation_folder
./configure --prefix ~  # Install at local home directory
make
make install
                    </div>
                </div>

                <h3>HPC Environment Setup</h3>
                <p>For High-Performance Computing environments, additional module loading is required:</p>

                <div class="command-block">
module load matlab  # Load MATLAB module
module load fastqc  # Optional: for quality control
module load multiqc # Optional: for aggregate QC reports
                </div>

                <div class="warning-box">
                    <strong>⚠️ MATLAB Requirements:</strong> MATLAB must be available from the command line and should have the Bioinformatics Toolbox and Image Processing Toolbox installed. These are essential for the CARLIN analysis component.
                </div>
            </div>
        </div>

        <!-- Section 3: Configuration -->
        <div class="section" id="configuration">
            <div class="section-header">
                ⚙️ 3. Configuration Management
            </div>
            <div class="section-content">
                <h3>config.yaml Structure</h3>
                <p>The pipeline is controlled through a YAML configuration file that specifies all experimental parameters and processing options:</p>

                <div class="config-box">
                    <h4>Example Configuration File</h4>
                    <div class="code-block">
project_name : 'Li_112219'
project_ID : '144505366'
SampleList : ['HSC','MPP','MyP']
cfg_type : 'sc10xV3'
template : 'cCARLIN'
read_cutoff_UMI_override : [3,10]
CARLIN_memory_factor : 300
sbatch : 1
CARLIN_max_run_time : 12
                    </div>
                </div>

                <div class="svg-container">
                    <svg width="750" height="450" viewBox="0 0 750 450">
                        <rect width="750" height="450" fill="#f8f9fa" rx="10"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Configuration Parameter Categories</text>

                        <!-- Project Settings -->
                        <rect x="50" y="60" width="160" height="100" fill="#e74c3c" rx="8"/>
                        <text x="130" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Project Settings</text>
                        <text x="130" y="100" text-anchor="middle" font-size="9" fill="white">• project_name</text>
                        <text x="130" y="115" text-anchor="middle" font-size="9" fill="white">• project_ID</text>
                        <text x="130" y="130" text-anchor="middle" font-size="9" fill="white">• SampleList</text>
                        <text x="130" y="145" text-anchor="middle" font-size="9" fill="white">Data identification</text>

                        <!-- Protocol Settings -->
                        <rect x="230" y="60" width="160" height="100" fill="#27ae60" rx="8"/>
                        <text x="310" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Protocol Settings</text>
                        <text x="310" y="100" text-anchor="middle" font-size="9" fill="white">• cfg_type</text>
                        <text x="310" y="115" text-anchor="middle" font-size="9" fill="white">• template</text>
                        <text x="310" y="130" text-anchor="middle" font-size="9" fill="white">Experimental protocol</text>
                        <text x="310" y="145" text-anchor="middle" font-size="9" fill="white">and primer sets</text>

                        <!-- Processing Settings -->
                        <rect x="410" y="60" width="160" height="100" fill="#3498db" rx="8"/>
                        <text x="490" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Processing Settings</text>
                        <text x="490" y="100" text-anchor="middle" font-size="9" fill="white">• read_cutoff_UMI</text>
                        <text x="490" y="115" text-anchor="middle" font-size="9" fill="white">Quality thresholds</text>
                        <text x="490" y="130" text-anchor="middle" font-size="9" fill="white">and filtering</text>
                        <text x="490" y="145" text-anchor="middle" font-size="9" fill="white">parameters</text>

                        <!-- Compute Settings -->
                        <rect x="590" y="60" width="160" height="100" fill="#9b59b6" rx="8"/>
                        <text x="670" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Compute Settings</text>
                        <text x="670" y="100" text-anchor="middle" font-size="9" fill="white">• sbatch mode</text>
                        <text x="670" y="115" text-anchor="middle" font-size="9" fill="white">• memory_factor</text>
                        <text x="670" y="130" text-anchor="middle" font-size="9" fill="white">• max_run_time</text>
                        <text x="670" y="145" text-anchor="middle" font-size="9" fill="white">HPC resources</text>

                        <!-- Parameter details -->
                        <rect x="50" y="190" width="650" height="230" fill="white" stroke="#bdc3c7" rx="10"/>
                        <text x="375" y="215" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Configuration Parameters Explained</text>

                        <!-- Left column -->
                        <text x="80" y="240" font-size="11" font-weight="bold" fill="#e74c3c">project_name & project_ID:</text>
                        <text x="100" y="255" font-size="10" fill="#333">Illumina BaseSpace project identifiers for automatic data download</text>

                        <text x="80" y="275" font-size="11" font-weight="bold" fill="#27ae60">SampleList:</text>
                        <text x="100" y="290" font-size="10" fill="#333">List of sample names to process (matches FASTQ file prefixes)</text>

                        <text x="80" y="310" font-size="11" font-weight="bold" fill="#3498db">cfg_type:</text>
                        <text x="100" y="325" font-size="10" fill="#333">Experimental protocol type (bulk vs single-cell, UMI structure)</text>

                        <text x="80" y="345" font-size="11" font-weight="bold" fill="#f39c12">template:</text>
                        <text x="100" y="360" font-size="10" fill="#333">Primer set and target locus (cCARLIN, Tigre, Rosa variants)</text>

                        <!-- Right column -->
                        <text x="400" y="240" font-size="11" font-weight="bold" fill="#9b59b6">read_cutoff_UMI_override:</text>
                        <text x="420" y="255" font-size="10" fill="#333">Minimum reads per UMI/cell barcode (list for multiple cutoffs)</text>

                        <text x="400" y="275" font-size="11" font-weight="bold" fill="#34495e">CARLIN_memory_factor:</text>
                        <text x="420" y="290" font-size="10" fill="#333">Memory allocation multiplier (300x FASTQ file size)</text>

                        <text x="400" y="310" font-size="11" font-weight="bold" fill="#e67e22">sbatch:</text>
                        <text x="420" y="325" font-size="10" fill="#333">Execution mode: 1 (SLURM batch) or 0 (interactive)</text>

                        <text x="400" y="345" font-size="11" font-weight="bold" fill="#16a085">CARLIN_max_run_time:</text>
                        <text x="420" y="360" font-size="10" fill="#333">Maximum job runtime in hours for HPC scheduling</text>

                        <!-- Memory calculation -->
                        <rect x="100" y="380" width="550" height="30" fill="#d4edda" stroke="#27ae60" rx="5"/>
                        <text x="375" y="400" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Memory Calculation: File Size × 300 (min: 20GB, max: 250GB)</text>
                    </svg>
                </div>

                <h3>Protocol Types (cfg_type)</h3>
                <p>The pipeline supports multiple experimental protocols, each with specific data structures:</p>

                <table class="pipeline-table">
                    <thead>
                        <tr>
                            <th>Protocol Type</th>
                            <th>Description</th>
                            <th>UMI Structure</th>
                            <th>Use Case</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>BulkRNA_Tigre_14UMI</strong></td>
                            <td>Bulk RNA from Tigre locus</td>
                            <td>14bp UMI</td>
                            <td>Bulk tissue analysis</td>
                        </tr>
                        <tr>
                            <td><strong>BulkRNA_Rosa_14UMI</strong></td>
                            <td>Bulk RNA from Rosa26 locus</td>
                            <td>14bp UMI</td>
                            <td>Bulk tissue analysis</td>
                        </tr>
                        <tr>
                            <td><strong>BulkRNA_12UMI</strong></td>
                            <td>Bulk RNA from Col1a1 locus</td>
                            <td>12bp UMI</td>
                            <td>Bulk tissue analysis</td>
                        </tr>
                        <tr>
                            <td><strong>scCamellia</strong></td>
                            <td>Single-cell Camellia-seq</td>
                            <td>Cell barcode + UMI</td>
                            <td>Multi-omics single-cell</td>
                        </tr>
                        <tr>
                            <td><strong>sc10xV3</strong></td>
                            <td>Single-cell 10X Genomics v3</td>
                            <td>10X barcode structure</td>
                            <td>Standard single-cell RNA-seq</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Template Selection</h3>
                <p>Templates correspond to different primer sets and target loci:</p>

                <div class="config-box">
                    <h4>Available Templates</h4>
                    <ul>
                        <li><strong>Short Primer Sets:</strong> Tigre_2022_v2, Rosa_v2, cCARLIN</li>
                        <li><strong>Long Primer Sets:</strong> Tigre_2022, Rosa, cCARLIN</li>
                    </ul>
                    <p><strong>Note:</strong> Col1a1 locus (cCARLIN) has only one primer set available</p>
                </div>

                <div class="key-feature">
                    <strong>🎯 Configuration Strategy:</strong> The pipeline automatically adapts processing parameters based on the selected protocol type and template, ensuring optimal analysis for each experimental design.
                </div>
            </div>
        </div>

        <!-- Section 4: Workflow Steps -->
        <div class="section" id="workflow">
            <div class="section-header">
                🔄 4. Workflow Steps and Execution
            </div>
            <div class="section-content">
                <h3>Complete Pipeline Workflow</h3>
                <p>The DARLIN pipeline consists of three main execution phases, each with specific objectives and outputs:</p>

                <div class="svg-container">
                    <svg width="800" height="550" viewBox="0 0 800 550">
                        <rect width="800" height="550" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">DARLIN Pipeline Execution Flow</text>

                        <!-- Phase 0: Data Acquisition -->
                        <rect x="50" y="60" width="700" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="10"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Phase 0: Data Acquisition (Optional)</text>

                        <rect x="100" y="105" width="150" height="25" fill="#3498db" rx="5"/>
                        <text x="175" y="122" text-anchor="middle" font-size="10" fill="white">BaseSpace Download</text>

                        <rect x="270" y="105" width="150" height="25" fill="#3498db" rx="5"/>
                        <text x="345" y="122" text-anchor="middle" font-size="10" fill="white">FASTQ Validation</text>

                        <rect x="440" y="105" width="150" height="25" fill="#3498db" rx="5"/>
                        <text x="515" y="122" text-anchor="middle" font-size="10" fill="white">File Organization</text>

                        <!-- Phase 1: Preprocessing -->
                        <rect x="50" y="170" width="700" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="10"/>
                        <text x="400" y="195" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Phase 1: Preprocessing and CARLIN Analysis</text>

                        <!-- Preprocessing steps -->
                        <rect x="80" y="220" width="120" height="60" fill="#27ae60" rx="8"/>
                        <text x="140" y="240" text-anchor="middle" font-size="10" fill="white">PEAR</text>
                        <text x="140" y="255" text-anchor="middle" font-size="10" fill="white">Read Merging</text>
                        <text x="140" y="270" text-anchor="middle" font-size="9" fill="white">R1 + R2 → Single</text>

                        <rect x="220" y="220" width="120" height="60" fill="#27ae60" rx="8"/>
                        <text x="280" y="240" text-anchor="middle" font-size="10" fill="white">FastQC</text>
                        <text x="280" y="255" text-anchor="middle" font-size="10" fill="white">Quality Control</text>
                        <text x="280" y="270" text-anchor="middle" font-size="9" fill="white">Before & After</text>

                        <rect x="360" y="220" width="120" height="60" fill="#27ae60" rx="8"/>
                        <text x="420" y="240" text-anchor="middle" font-size="10" fill="white">CARLIN</text>
                        <text x="420" y="255" text-anchor="middle" font-size="10" fill="white">Analysis</text>
                        <text x="420" y="270" text-anchor="middle" font-size="9" fill="white">MATLAB Engine</text>

                        <rect x="500" y="220" width="120" height="60" fill="#27ae60" rx="8"/>
                        <text x="560" y="240" text-anchor="middle" font-size="10" fill="white">Allele</text>
                        <text x="560" y="255" text-anchor="middle" font-size="10" fill="white">Annotation</text>
                        <text x="560" y="270" text-anchor="middle" font-size="9" fill="white">Per Sample</text>

                        <!-- Phase 2: Aggregation -->
                        <rect x="50" y="320" width="700" height="120" fill="#fff3cd" stroke="#f39c12" stroke-width="3" rx="10"/>
                        <text x="400" y="345" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Phase 2: Aggregation and Reporting</text>

                        <!-- Aggregation steps -->
                        <rect x="80" y="370" width="120" height="60" fill="#f39c12" rx="8"/>
                        <text x="140" y="390" text-anchor="middle" font-size="10" fill="white">Sample</text>
                        <text x="140" y="405" text-anchor="middle" font-size="10" fill="white">Merging</text>
                        <text x="140" y="420" text-anchor="middle" font-size="9" fill="white">Cross-sample</text>

                        <rect x="220" y="370" width="120" height="60" fill="#f39c12" rx="8"/>
                        <text x="280" y="390" text-anchor="middle" font-size="10" fill="white">Statistics</text>
                        <text x="280" y="405" text-anchor="middle" font-size="10" fill="white">Generation</text>
                        <text x="280" y="420" text-anchor="middle" font-size="9" fill="white">CSV outputs</text>

                        <rect x="360" y="370" width="120" height="60" fill="#f39c12" rx="8"/>
                        <text x="420" y="390" text-anchor="middle" font-size="10" fill="white">Visualization</text>
                        <text x="420" y="405" text-anchor="middle" font-size="10" fill="white">Plots</text>
                        <text x="420" y="420" text-anchor="middle" font-size="9" fill="white">PNG graphics</text>

                        <rect x="500" y="370" width="120" height="60" fill="#f39c12" rx="8"/>
                        <text x="560" y="390" text-anchor="middle" font-size="10" fill="white">HTML</text>
                        <text x="560" y="405" text-anchor="middle" font-size="10" fill="white">Report</text>
                        <text x="560" y="420" text-anchor="middle" font-size="9" fill="white">Jupyter NB</text>

                        <!-- Arrows between phases -->
                        <path d="M 400 150 L 400 170" stroke="#666" stroke-width="4" marker-end="url(#arrowhead2)"/>
                        <path d="M 400 300 L 400 320" stroke="#666" stroke-width="4" marker-end="url(#arrowhead2)"/>

                        <!-- Command examples -->
                        <rect x="50" y="470" width="700" height="60" fill="white" stroke="#34495e" stroke-width="2" rx="8"/>
                        <text x="400" y="490" text-anchor="middle" font-size="14" font-weight="bold" fill="#34495e">Execution Commands</text>
                        <text x="120" y="510" font-size="9" fill="#333">Data: snakefile_get_data.py</text>
                        <text x="320" y="510" font-size="9" fill="#333">Part1: snakefile_matlab_DARLIN_Part1.py</text>
                        <text x="580" y="510" font-size="9" fill="#333">Part2: snakefile_matlab_DARLIN_Part2.py</text>

                        <defs>
                            <marker id="arrowhead2" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Detailed Execution Steps</h3>

                <div class="pipeline-step">
                    <span class="step-number">0</span>
                    <strong>Data Acquisition (Optional)</strong>
                    <p>Download FASTQ files from Illumina BaseSpace if not already available locally:</p>
                    <div class="command-block">
# First authenticate with BaseSpace
bs auth
bs list project

# Then run data acquisition
snakemake -s $code_directory/snakemake_DARLIN/snakefiles/snakefile_get_data.py \
    --configfile config.yaml --core 1
                    </div>
                </div>

                <div class="pipeline-step">
                    <span class="step-number">1</span>
                    <strong>Part 1: Preprocessing and CARLIN Analysis</strong>
                    <p>Main processing phase that handles read merging, quality control, and CARLIN analysis:</p>
                    <div class="command-block">
conda activate snakemake_darlin

snakemake -s $code_directory/snakemake_DARLIN/snakefiles/snakefile_matlab_DARLIN_Part1.py \
    --configfile config.yaml --core 10
                    </div>
                    <p><strong>What happens in Part 1:</strong></p>
                    <ul>
                        <li>PEAR merges paired-end reads (for bulk protocols)</li>
                        <li>FastQC generates quality reports</li>
                        <li>CARLIN pipeline processes each sample independently</li>
                        <li>Allele annotation and frequency counting</li>
                    </ul>
                </div>

                <div class="pipeline-step">
                    <span class="step-number">2</span>
                    <strong>Part 2: Aggregation and Reporting</strong>
                    <p>Combines results across samples and generates comprehensive reports:</p>
                    <div class="command-block">
snakemake -s $code_directory/snakemake_DARLIN/snakefiles/snakefile_matlab_DARLIN_Part2.py \
    --configfile config.yaml --core 5 --ri -R generate_report -R plots
                    </div>
                    <p><strong>What happens in Part 2:</strong></p>
                    <ul>
                        <li>Merges allele data across all samples</li>
                        <li>Generates summary statistics CSV files</li>
                        <li>Creates visualization plots</li>
                        <li>Produces HTML quality control reports</li>
                    </ul>
                </div>

                <h3>Resource Management</h3>
                <p>The pipeline includes intelligent resource allocation based on data size:</p>

                <div class="formula-box">
                    <h4>Memory Allocation Formula</h4>
                    $$\text{Requested Memory (GB)} = \max(20, \min(250, \text{File Size (GB)} \times 300))$$
                    <p>This ensures adequate memory for CARLIN analysis while respecting HPC limits</p>
                </div>
            </div>
        </div>

        <!-- Section 5: Analysis Methods -->
        <div class="section" id="analysis">
            <div class="section-header">
                🔬 5. Analysis Methods and Algorithms
            </div>
            <div class="section-content">
                <h3>PEAR Read Merging Strategy</h3>
                <p>For bulk protocols, PEAR (Paired-End reAd mergeR) combines R1 and R2 reads to reconstruct full-length DARLIN sequences:</p>

                <div class="svg-container">
                    <svg width="700" height="350" viewBox="0 0 700 350">
                        <rect width="700" height="350" fill="#f8f9fa" rx="10"/>

                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">PEAR Read Merging Process</text>

                        <!-- R1 read -->
                        <rect x="50" y="60" width="200" height="25" fill="#3498db" rx="3"/>
                        <text x="150" y="77" text-anchor="middle" font-size="10" fill="white">R1 Read (Forward)</text>

                        <!-- R2 read -->
                        <rect x="450" y="60" width="200" height="25" fill="#e74c3c" rx="3"/>
                        <text x="550" y="77" text-anchor="middle" font-size="10" fill="white">R2 Read (Reverse)</text>

                        <!-- Overlap region -->
                        <rect x="200" y="110" width="300" height="25" fill="#f39c12" rx="3"/>
                        <text x="350" y="127" text-anchor="middle" font-size="10" fill="white">Overlap Region</text>

                        <!-- Merged read -->
                        <rect x="100" y="160" width="500" height="25" fill="#27ae60" rx="3"/>
                        <text x="350" y="177" text-anchor="middle" font-size="10" fill="white">Merged Read (Full DARLIN Sequence)</text>

                        <!-- Arrows -->
                        <path d="M 150 85 L 300 110" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 550 85 L 400 110" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 350 135 L 350 160" stroke="#f39c12" stroke-width="3" marker-end="url(#arrowhead3)"/>

                        <!-- PEAR parameters -->
                        <rect x="100" y="210" width="500" height="120" fill="white" stroke="#bdc3c7" rx="8"/>
                        <text x="350" y="230" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">PEAR Parameters</text>

                        <text x="130" y="250" font-size="11" font-weight="bold" fill="#333">--min-overlap 1:</text>
                        <text x="250" y="250" font-size="10" fill="#666">Minimum overlap between R1 and R2</text>

                        <text x="130" y="270" font-size="11" font-weight="bold" fill="#333">--min-assembly-length 1:</text>
                        <text x="280" y="270" font-size="10" fill="#666">Minimum merged read length</text>

                        <text x="130" y="290" font-size="11" font-weight="bold" fill="#333">--quality-threshold 30:</text>
                        <text x="270" y="290" font-size="10" fill="#666">Phred quality score cutoff</text>

                        <text x="130" y="310" font-size="11" font-weight="bold" fill="#333">-j 16:</text>
                        <text x="170" y="310" font-size="10" fill="#666">Number of parallel threads</text>

                        <defs>
                            <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>CARLIN Analysis Integration</h3>
                <p>The pipeline integrates with a customized CARLIN MATLAB pipeline for barcode analysis:</p>

                <div class="pipeline-step">
                    <span class="step-number">3</span>
                    <strong>CARLIN MATLAB Function Call:</strong>
                    <div class="code-block">
my_CARLIN_pipeline(
    sample,                    % Sample name
    cfg_type,                  % Protocol configuration
    input_dir,                 % Input directory path
    output_dir,                % Output directory path
    template,                  % Primer template
    'read_cutoff_UMI_override', read_cutoff_UMI,
    'read_cutoff_CB_override',  read_cutoff_CB,
    'CARLIN_dir',              CARLIN_dir
)
                    </div>
                </div>

                <h3>Quality Control Metrics</h3>
                <p>The pipeline generates comprehensive quality control metrics at multiple stages:</p>

                <table class="pipeline-table">
                    <thead>
                        <tr>
                            <th>QC Stage</th>
                            <th>Tool</th>
                            <th>Metrics Generated</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Pre-processing</strong></td>
                            <td>FastQC</td>
                            <td>Read quality, GC content, adapter content</td>
                            <td>Assess raw data quality</td>
                        </tr>
                        <tr>
                            <td><strong>Post-merging</strong></td>
                            <td>FastQC</td>
                            <td>Merged read quality, length distribution</td>
                            <td>Validate PEAR merging success</td>
                        </tr>
                        <tr>
                            <td><strong>CARLIN Analysis</strong></td>
                            <td>Custom metrics</td>
                            <td>Editing efficiency, allele diversity</td>
                            <td>Evaluate DARLIN performance</td>
                        </tr>
                        <tr>
                            <td><strong>Final Report</strong></td>
                            <td>MultiQC + Custom</td>
                            <td>Aggregate statistics, visualizations</td>
                            <td>Comprehensive quality assessment</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Allele Frequency Analysis</h3>
                <p>The pipeline includes sophisticated allele frequency analysis methods:</p>

                <div class="method-box">
                    <span class="method-number">4</span>
                    <strong>Cumulative Insertion/Deletion Analysis:</strong> Plots cumulative frequency distributions of insertion and deletion lengths to characterize editing patterns and compare with theoretical expectations.
                </div>

                <div class="formula-box">
                    <h4>Allele Frequency Metrics</h4>
                    <p><strong>Shannon Diversity:</strong></p>
                    $$H = -\sum_{i=1}^{n} p_i \log_2 p_i$$

                    <p><strong>Singleton Fraction:</strong></p>
                    $$f_{singleton} = \frac{\text{Alleles with count = 1}}{\text{Total unique alleles}}$$

                    <p><strong>Editing Efficiency:</strong></p>
                    $$\text{Efficiency} = \frac{\text{Edited alleles}}{\text{Total detected alleles}}$$
                </div>

                <div class="key-feature">
                    <strong>🎯 Analysis Innovation:</strong> The pipeline automatically generates insertion pattern analysis, revealing the characteristic TdT-mediated insertion signatures that distinguish DARLIN from standard Cas9 editing.
                </div>
            </div>
        </div>

        <!-- Section 6: Outputs and QC -->
        <div class="section" id="outputs">
            <div class="section-header">
                📁 6. Outputs and Quality Control
            </div>
            <div class="section-content">
                <h3>Generated File Structure</h3>
                <p>The DARLIN pipeline creates a well-organized directory structure with all analysis outputs:</p>

                <div class="svg-container">
                    <svg width="750" height="500" viewBox="0 0 750 500">
                        <rect width="750" height="500" fill="#f8f9fa" rx="10"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">DARLIN Pipeline Output Structure</text>

                        <!-- Root directory -->
                        <rect x="300" y="50" width="150" height="30" fill="#34495e" rx="5"/>
                        <text x="375" y="70" text-anchor="middle" font-size="12" fill="white">Project Root</text>

                        <!-- Main output directories -->
                        <rect x="50" y="120" width="120" height="40" fill="#3498db" rx="5"/>
                        <text x="110" y="135" text-anchor="middle" font-size="10" fill="white">raw_fastq/</text>
                        <text x="110" y="150" text-anchor="middle" font-size="9" fill="white">Input data</text>

                        <rect x="190" y="120" width="120" height="40" fill="#27ae60" rx="5"/>
                        <text x="250" y="135" text-anchor="middle" font-size="10" fill="white">pear_output/</text>
                        <text x="250" y="150" text-anchor="middle" font-size="9" fill="white">Merged reads</text>

                        <rect x="330" y="120" width="120" height="40" fill="#f39c12" rx="5"/>
                        <text x="390" y="135" text-anchor="middle" font-size="10" fill="white">DARLIN/</text>
                        <text x="390" y="150" text-anchor="middle" font-size="9" fill="white">Main results</text>

                        <rect x="470" y="120" width="120" height="40" fill="#9b59b6" rx="5"/>
                        <text x="530" y="135" text-anchor="middle" font-size="10" fill="white">fastqc_*/</text>
                        <text x="530" y="150" text-anchor="middle" font-size="9" fill="white">QC reports</text>

                        <rect x="610" y="120" width="120" height="40" fill="#e74c3c" rx="5"/>
                        <text x="670" y="135" text-anchor="middle" font-size="10" fill="white">log/</text>
                        <text x="670" y="150" text-anchor="middle" font-size="9" fill="white">Job logs</text>

                        <!-- DARLIN subdirectory structure -->
                        <rect x="250" y="200" width="250" height="120" fill="#fff3cd" stroke="#f39c12" rx="8"/>
                        <text x="375" y="220" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">DARLIN/ Directory Structure</text>

                        <rect x="270" y="240" width="210" height="20" fill="#f39c12" rx="3"/>
                        <text x="375" y="253" text-anchor="middle" font-size="9" fill="white">results_cutoff_override_X/</text>

                        <rect x="290" y="270" width="80" height="15" fill="#27ae60" rx="2"/>
                        <text x="330" y="280" text-anchor="middle" font-size="8" fill="white">Sample1/</text>

                        <rect x="380" y="270" width="80" height="15" fill="#27ae60" rx="2"/>
                        <text x="420" y="280" text-anchor="middle" font-size="8" fill="white">Sample2/</text>

                        <rect x="290" y="295" width="170" height="15" fill="#3498db" rx="2"/>
                        <text x="375" y="305" text-anchor="middle" font-size="8" fill="white">merge_all/</text>

                        <!-- Sample directory contents -->
                        <rect x="50" y="350" width="650" height="130" fill="white" stroke="#bdc3c7" rx="8"/>
                        <text x="375" y="370" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Output Files</text>

                        <!-- Per-sample files -->
                        <text x="80" y="390" font-size="11" font-weight="bold" fill="#27ae60">Per-Sample Files:</text>
                        <text x="100" y="405" font-size="10" fill="#333">• Summary.mat - MATLAB analysis results</text>
                        <text x="100" y="420" font-size="10" fill="#333">• allele_annotation.mat - Allele frequencies and annotations</text>
                        <text x="100" y="435" font-size="10" fill="#333">• allele_UMI_count.csv - Allele frequency table</text>
                        <text x="100" y="450" font-size="10" fill="#333">• DARLIN_analysis.done - Completion flag</text>

                        <!-- Merged files -->
                        <text x="400" y="390" font-size="11" font-weight="bold" fill="#3498db">Merged Files (merge_all/):</text>
                        <text x="420" y="405" font-size="10" fill="#333">• refined_results.csv - Summary statistics</text>
                        <text x="420" y="420" font-size="10" fill="#333">• DARLIN_report.html - QC report</text>
                        <text x="420" y="435" font-size="10" fill="#333">• *.png - Visualization plots</text>
                        <text x="420" y="450" font-size="10" fill="#333">• allele_UMI_count.csv - Combined data</text>

                        <!-- Arrows from root -->
                        <path d="M 375 80 L 110 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 375 80 L 250 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 375 80 L 390 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 375 80 L 530 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 375 80 L 670 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>

                        <defs>
                            <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>HTML Reporting System</h3>
                <p>The pipeline generates comprehensive HTML reports using Jupyter notebooks and Papermill:</p>

                <div class="method-box">
                    <span class="method-number">5</span>
                    <strong>Automated Report Generation:</strong> Uses Papermill to execute parameterized Jupyter notebooks, generating HTML reports with embedded plots, statistics, and quality metrics for each analysis run.
                </div>

                <h3>Visualization Outputs</h3>
                <p>The pipeline automatically generates multiple types of visualizations:</p>

                <div class="svg-container">
                    <svg width="700" height="300" viewBox="0 0 700 300">
                        <rect width="700" height="300" fill="#f8f9fa" rx="10"/>

                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Generated Visualizations</text>

                        <!-- Plot types -->
                        <rect x="50" y="50" width="130" height="60" fill="#e74c3c" rx="8"/>
                        <text x="115" y="70" text-anchor="middle" font-size="10" fill="white">Allele Frequency</text>
                        <text x="115" y="85" text-anchor="middle" font-size="10" fill="white">Distributions</text>
                        <text x="115" y="100" text-anchor="middle" font-size="9" fill="white">Per sample & merged</text>

                        <rect x="200" y="50" width="130" height="60" fill="#27ae60" rx="8"/>
                        <text x="265" y="70" text-anchor="middle" font-size="10" fill="white">Insertion/Deletion</text>
                        <text x="265" y="85" text-anchor="middle" font-size="10" fill="white">Patterns</text>
                        <text x="265" y="100" text-anchor="middle" font-size="9" fill="white">Cumulative plots</text>

                        <rect x="350" y="50" width="130" height="60" fill="#3498db" rx="8"/>
                        <text x="415" y="70" text-anchor="middle" font-size="10" fill="white">Data Statistics</text>
                        <text x="415" y="85" text-anchor="middle" font-size="10" fill="white">Across Samples</text>
                        <text x="415" y="100" text-anchor="middle" font-size="9" fill="white">Comparative analysis</text>

                        <rect x="500" y="50" width="130" height="60" fill="#9b59b6" rx="8"/>
                        <text x="565" y="70" text-anchor="middle" font-size="10" fill="white">Quality Control</text>
                        <text x="565" y="85" text-anchor="middle" font-size="10" fill="white">Metrics</text>
                        <text x="565" y="100" text-anchor="middle" font-size="9" fill="white">FastQC/MultiQC</text>

                        <!-- Function calls -->
                        <rect x="100" y="140" width="500" height="120" fill="white" stroke="#bdc3c7" rx="8"/>
                        <text x="350" y="160" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Analysis Functions</text>

                        <text x="120" y="180" font-size="10" font-weight="bold" fill="#333">plot_cumulative_insert_del_freq():</text>
                        <text x="140" y="195" font-size="9" fill="#666">Generates cumulative frequency plots for insertions and deletions</text>

                        <text x="120" y="210" font-size="10" font-weight="bold" fill="#333">plot_data_statistics_across_samples():</text>
                        <text x="140" y="225" font-size="9" fill="#666">Creates comparative statistics visualizations</text>

                        <text x="120" y="240" font-size="10" fill="#333">plot_insertion_patterns():</text>
                        <text x="140" y="255" font-size="9" fill="#666">Analyzes and visualizes TdT insertion signatures</text>
                    </svg>
                </div>

                <h3>Testing and Validation</h3>
                <p>The pipeline includes comprehensive testing to ensure proper installation and functionality:</p>

                <div class="pipeline-step">
                    <span class="step-number">6</span>
                    <strong>Pipeline Testing:</strong>
                    <div class="command-block">
cd snakemake_DARLIN/test
bash test.sh
                    </div>
                    <p>This runs test cases for three protocol types: bulk, sc-10X, and scCamellia</p>
                </div>

                <div class="warning-box">
                    <strong>⚠️ Expected Test Outputs:</strong> The test should generate analysis results for all three test datasets. Check the log file and output directories to verify successful completion.
                </div>

                <h3>Troubleshooting Common Issues</h3>
                <table class="pipeline-table">
                    <thead>
                        <tr>
                            <th>Issue</th>
                            <th>Cause</th>
                            <th>Solution</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>MATLAB not found</strong></td>
                            <td>MATLAB not in PATH</td>
                            <td>Load MATLAB module or add to PATH</td>
                        </tr>
                        <tr>
                            <td><strong>PEAR command failed</strong></td>
                            <td>PEAR not installed locally</td>
                            <td>Install PEAR with --prefix option</td>
                        </tr>
                        <tr>
                            <td><strong>Memory allocation error</strong></td>
                            <td>Insufficient HPC resources</td>
                            <td>Adjust CARLIN_memory_factor</td>
                        </tr>
                        <tr>
                            <td><strong>Template not found</strong></td>
                            <td>Incorrect template specification</td>
                            <td>Check available templates in config</td>
                        </tr>
                        <tr>
                            <td><strong>Job timeout</strong></td>
                            <td>Insufficient runtime allocation</td>
                            <td>Increase CARLIN_max_run_time</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Performance Optimization</h3>
                <div class="key-feature">
                    <strong>🚀 Optimization Strategies:</strong>
                    <ul>
                        <li><strong>Parallel Processing:</strong> Multiple samples processed simultaneously</li>
                        <li><strong>Memory Scaling:</strong> Dynamic memory allocation based on file size</li>
                        <li><strong>Job Scheduling:</strong> Intelligent SLURM job submission</li>
                        <li><strong>Checkpoint System:</strong> Resume from interruption points</li>
                    </ul>
                </div>

                <h3>Pipeline Maintenance</h3>
                <div class="pipeline-step">
                    <span class="step-number">7</span>
                    <strong>Keeping Pipeline Updated:</strong>
                    <div class="command-block">
cd $code_directory
cd snakemake_DARLIN
git pull

cd ../CARLIN_pipeline/Custom_CARLIN
git pull

cd ../../MosaicLineage
git pull
                    </div>
                </div>

                <div class="formula-box">
                    <h4>Pipeline Performance Metrics</h4>
                    <p><strong>Processing Speed:</strong></p>
                    $$\text{Time per Sample} \approx \frac{\text{File Size (GB)}}{10} \text{ hours}$$

                    <p><strong>Memory Usage:</strong></p>
                    $$\text{Peak Memory} = \text{File Size} \times 300 \text{ (GB)}$$

                    <p><strong>Success Rate:</strong> >95% for properly configured runs</p>
                </div>

                <div class="key-feature">
                    <strong>💡 Pipeline Benefits:</strong> The DARLIN Snakemake pipeline reduces analysis time from days to hours, eliminates manual processing errors, and provides standardized, reproducible results across different computational environments and experimental protocols.
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding: 40px; color: white; background: rgba(0,0,0,0.1); border-radius: 15px; margin-top: 30px;">
            <p style="font-size: 1.2em; font-weight: bold;">DARLIN Snakemake Pipeline Tutorial</p>
            <p>Automated Processing for High-Resolution Lineage Tracing</p>
            <p style="margin-top: 15px; font-size: 0.9em;">Based on: ShouWenWang-Lab/snakemake_DARLIN GitHub Repository</p>
            <p style="margin-top: 10px; font-size: 0.9em;">Li et al. (2023) Cell - DARLIN Project</p>
        </div>
    </div>
</body>
</html>
