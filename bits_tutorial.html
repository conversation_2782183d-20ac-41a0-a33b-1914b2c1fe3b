<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Interval Tree Search (BITS) Algorithm Tutorial</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 0.5rem 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 5px solid #3498db;
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 1.5rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 4px solid #e74c3c;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 1rem 0;
            border: 1px solid #34495e;
        }
        
        .rust-code {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 1rem 0;
            border-left: 4px solid #f39c12;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .nav {
            background: #34495e;
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
        }
        
        .nav li {
            margin-right: 2rem;
        }
        
        .nav a {
            color: #ecf0f1;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav a:hover {
            color: #3498db;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .formula {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .warning {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 4px solid #e17055;
        }
        
        .info {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
        
        .algorithm-box {
            background: linear-gradient(135deg, #55a3ff 0%, #003d82 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
        }
        
        .performance-box {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Binary Interval Tree Search (BITS)</h1>
            <p>A Scalable Algorithm for Counting Interval Intersections</p>
        </div>
        
        <div class="nav">
            <ul>
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#problem">Problem Definition</a></li>
                <li><a href="#theory">Algorithm Theory</a></li>
                <li><a href="#implementation">Implementation</a></li>
                <li><a href="#performance">Performance</a></li>
                <li><a href="#applications">Applications</a></li>
                <li><a href="#comparison">Comparisons</a></li>
            </ul>
        </div>
        
        <div class="content">
            <section id="introduction" class="section">
                <h2>1. Introduction to BITS Algorithm</h2>

                <div class="highlight">
                    <strong>BITS (Binary Interval Search)</strong> is a novel and scalable algorithm for counting interval intersections, particularly optimized for genomic data analysis and parallel computing architectures.
                </div>

                <h3>What is Interval Intersection?</h3>
                <p>Interval intersection is a fundamental operation in computational biology where we need to find overlapping genomic regions. Two intervals <code>a</code> and <code>b</code> intersect when:</p>

                <div class="formula">
                    $$a.start \leq b.end \text{ and } a.end \geq b.start$$
                </div>

                <div class="svg-container">
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <defs>
                            <linearGradient id="intervalA" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="intervalB" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="overlap" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
                            </linearGradient>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                    refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
                            </marker>
                        </defs>

                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Interval Intersection Examples</text>

                        <!-- Coordinate axis -->
                        <line x1="50" y1="80" x2="750" y2="80" stroke="#34495e" stroke-width="2"/>
                        <text x="50" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">0</text>
                        <text x="150" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">10</text>
                        <text x="250" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">20</text>
                        <text x="350" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">30</text>
                        <text x="450" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">40</text>
                        <text x="550" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">50</text>
                        <text x="650" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">60</text>
                        <text x="750" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">70</text>

                        <!-- Example 1: Overlapping intervals -->
                        <text x="50" y="120" fill="#2c3e50" font-size="14" font-weight="bold">Case 1: Overlapping Intervals</text>

                        <!-- Interval A -->
                        <rect x="150" y="130" width="200" height="20" fill="url(#intervalA)" rx="3"/>
                        <text x="250" y="145" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Interval A [10, 30)</text>

                        <!-- Interval B -->
                        <rect x="250" y="160" width="200" height="20" fill="url(#intervalB)" rx="3"/>
                        <text x="350" y="175" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Interval B [20, 40)</text>

                        <!-- Overlap region -->
                        <rect x="250" y="190" width="100" height="15" fill="url(#overlap)" rx="3"/>
                        <text x="300" y="202" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Overlap [20, 30)</text>

                        <!-- Example 2: Non-overlapping intervals -->
                        <text x="450" y="120" fill="#2c3e50" font-size="14" font-weight="bold">Case 2: Non-overlapping Intervals</text>

                        <!-- Interval C -->
                        <rect x="450" y="130" width="100" height="20" fill="url(#intervalA)" rx="3"/>
                        <text x="500" y="145" text-anchor="middle" fill="white" font-size="12" font-weight="bold">C [40, 50)</text>

                        <!-- Interval D -->
                        <rect x="600" y="160" width="100" height="20" fill="url(#intervalB)" rx="3"/>
                        <text x="650" y="175" text-anchor="middle" fill="white" font-size="12" font-weight="bold">D [55, 65)</text>

                        <text x="575" y="200" text-anchor="middle" fill="#e74c3c" font-size="12" font-weight="bold">No Overlap</text>

                        <!-- Mathematical condition -->
                        <text x="400" y="250" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Intersection Condition:</text>
                        <text x="400" y="270" text-anchor="middle" fill="#34495e" font-size="12">a.start ≤ b.end AND a.end ≥ b.start</text>
                    </svg>
                </div>

                <h3>Why BITS Matters</h3>
                <div class="info">
                    <ul>
                        <li><strong>Genomic Scale:</strong> Modern genomics datasets contain billions of intervals</li>
                        <li><strong>Performance:</strong> 3-4x faster than traditional methods on genomic data</li>
                        <li><strong>Parallelization:</strong> Inherently suited for GPU and multi-core architectures</li>
                        <li><strong>Optimal Complexity:</strong> O(N log N) time complexity, which is theoretically optimal</li>
                        <li><strong>Memory Efficient:</strong> Uses simple sorted arrays instead of complex tree structures</li>
                    </ul>
                </div>

                <h3>Key Innovation</h3>
                <div class="highlight">
                    Unlike traditional methods that enumerate intersections to count them, BITS uses an <strong>exclusion-based approach</strong>: it identifies intervals that <em>cannot</em> intersect and infers the count of intersecting intervals from the remainder.
                </div>

                <h3>Applications in Genomics</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Application</th>
                                <th>Description</th>
                                <th>Typical Dataset Size</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>ChIP-seq Analysis</td>
                                <td>Finding transcription factor binding sites</td>
                                <td>Millions of peaks</td>
                            </tr>
                            <tr>
                                <td>RNA-seq Quantification</td>
                                <td>Counting reads overlapping genes/exons</td>
                                <td>Billions of reads</td>
                            </tr>
                            <tr>
                                <td>Variant Analysis</td>
                                <td>Intersecting variants with genomic features</td>
                                <td>Millions of variants</td>
                            </tr>
                            <tr>
                                <td>Copy Number Analysis</td>
                                <td>Finding CNV overlaps with genes</td>
                                <td>Thousands of CNVs</td>
                            </tr>
                            <tr>
                                <td>Regulatory Analysis</td>
                                <td>Overlapping enhancers with target genes</td>
                                <td>Hundreds of thousands</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section id="problem" class="section">
                <h2>2. Problem Definition and Challenges</h2>

                <h3>The Interval Set Intersection Problem</h3>
                <p>Given two sets of intervals A = {a₁, a₂, ..., aₙ} and B = {b₁, b₂, ..., bₘ}, we want to solve several related problems:</p>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Problem Type</th>
                                <th>Description</th>
                                <th>Output</th>
                                <th>BITS Efficiency</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Decision</strong></td>
                                <td>Does at least one interval in A intersect an interval in B?</td>
                                <td>Boolean</td>
                                <td>Excellent</td>
                            </tr>
                            <tr>
                                <td><strong>Counting</strong></td>
                                <td>How many total intersections exist between A and B?</td>
                                <td>Integer count</td>
                                <td>Excellent</td>
                            </tr>
                            <tr>
                                <td><strong>Per-interval counting</strong></td>
                                <td>For each interval in A, how many intervals in B intersect it?</td>
                                <td>Array of counts</td>
                                <td>Excellent</td>
                            </tr>
                            <tr>
                                <td><strong>Enumeration</strong></td>
                                <td>What are all the pairwise intersections?</td>
                                <td>Set of interval pairs</td>
                                <td>Good</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Challenges with Naive Approaches</h3>

                <div class="svg-container">
                    <svg width="900" height="400" viewBox="0 0 900 400">
                        <defs>
                            <linearGradient id="problemGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="solutionGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">The Contained Interval Problem</text>

                        <!-- Database intervals -->
                        <text x="50" y="60" fill="#2c3e50" font-size="14" font-weight="bold">Database Intervals (sorted by start):</text>

                        <!-- Interval representations -->
                        <rect x="100" y="80" width="60" height="20" fill="#3498db" rx="3"/>
                        <text x="130" y="95" text-anchor="middle" fill="white" font-size="10">a [5,15]</text>

                        <rect x="180" y="80" width="40" height="20" fill="#3498db" rx="3"/>
                        <text x="200" y="95" text-anchor="middle" fill="white" font-size="10">b [8,12]</text>

                        <rect x="240" y="80" width="80" height="20" fill="#3498db" rx="3"/>
                        <text x="280" y="95" text-anchor="middle" fill="white" font-size="10">c [10,18]</text>

                        <rect x="340" y="80" width="60" height="20" fill="#3498db" rx="3"/>
                        <text x="370" y="95" text-anchor="middle" fill="white" font-size="10">d [15,21]</text>

                        <rect x="420" y="80" width="40" height="20" fill="#3498db" rx="3"/>
                        <text x="440" y="95" text-anchor="middle" fill="white" font-size="10">e [20,24]</text>

                        <!-- Query interval -->
                        <text x="50" y="140" fill="#2c3e50" font-size="14" font-weight="bold">Query Interval:</text>
                        <rect x="200" y="150" width="100" height="25" fill="url(#problemGrad)" rx="3"/>
                        <text x="250" y="167" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Query [9, 19]</text>

                        <!-- Problem illustration -->
                        <text x="50" y="210" fill="#2c3e50" font-size="14" font-weight="bold">Naive Binary Search Problem:</text>

                        <!-- Step 1 -->
                        <text x="70" y="235" fill="#34495e" font-size="12">1. Binary search for query.end (19) in starts: finds interval d</text>
                        <text x="70" y="255" fill="#34495e" font-size="12">2. Check if d intersects query: YES [15,21] ∩ [9,19] = [15,19]</text>
                        <text x="70" y="275" fill="#e74c3c" font-size="12">3. Scan backwards to find more intersections...</text>
                        <text x="70" y="295" fill="#e74c3c" font-size="12">4. Problem: Interval b [8,12] is contained in a [5,15]</text>
                        <text x="70" y="315" fill="#e74c3c" font-size="12">5. Must scan all the way to beginning - O(N) worst case!</text>

                        <!-- Visual representation of the problem -->
                        <text x="500" y="140" fill="#2c3e50" font-size="14" font-weight="bold">Visual Representation:</text>

                        <!-- Timeline -->
                        <line x1="500" y1="170" x2="850" y2="170" stroke="#34495e" stroke-width="2"/>
                        <text x="500" y="165" text-anchor="middle" fill="#7f8c8d" font-size="10">5</text>
                        <text x="600" y="165" text-anchor="middle" fill="#7f8c8d" font-size="10">10</text>
                        <text x="700" y="165" text-anchor="middle" fill="#7f8c8d" font-size="10">15</text>
                        <text x="800" y="165" text-anchor="middle" fill="#7f8c8d" font-size="10">20</text>

                        <!-- Intervals on timeline -->
                        <rect x="500" y="180" width="100" height="15" fill="#3498db" fill-opacity="0.7" rx="2"/>
                        <text x="550" y="192" text-anchor="middle" fill="#2c3e50" font-size="9">a [5,15]</text>

                        <rect x="530" y="200" width="40" height="15" fill="#e74c3c" fill-opacity="0.7" rx="2"/>
                        <text x="550" y="212" text-anchor="middle" fill="#2c3e50" font-size="9">b [8,12]</text>

                        <rect x="580" y="220" width="80" height="15" fill="#3498db" fill-opacity="0.7" rx="2"/>
                        <text x="620" y="232" text-anchor="middle" fill="#2c3e50" font-size="9">c [10,18]</text>

                        <rect x="700" y="240" width="60" height="15" fill="#3498db" fill-opacity="0.7" rx="2"/>
                        <text x="730" y="252" text-anchor="middle" fill="#2c3e50" font-size="9">d [15,21]</text>

                        <!-- Query interval -->
                        <rect x="570" y="270" width="100" height="20" fill="url(#problemGrad)" fill-opacity="0.8" rx="3"/>
                        <text x="620" y="283" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Query [9,19]</text>

                        <!-- Contained interval highlight -->
                        <circle cx="550" cy="207" r="25" fill="none" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="550" y="320" text-anchor="middle" fill="#e74c3c" font-size="11" font-weight="bold">Contained Interval!</text>
                        <text x="550" y="335" text-anchor="middle" fill="#e74c3c" font-size="10">Breaks naive binary search</text>

                        <!-- Arrow pointing to problem -->
                        <path d="M 550 300 Q 550 280 550 225" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    </svg>
                </div>

                <div class="warning">
                    <strong>The Contained Interval Problem:</strong> When intervals are nested inside other intervals, naive binary search approaches fail because:
                    <ul>
                        <li>A single binary search cannot determine all intersecting intervals</li>
                        <li>Backward scanning may need to traverse the entire dataset</li>
                        <li>Time complexity degrades to O(N²) in worst case</li>
                        <li>Unpredictable performance based on data distribution</li>
                    </ul>
                </div>

                <h3>Limitations of Existing Methods</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Method</th>
                                <th>Data Structure</th>
                                <th>Time Complexity</th>
                                <th>Parallelization</th>
                                <th>Main Limitation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>R-trees (UCSC)</td>
                                <td>Hierarchical bins</td>
                                <td>O(N²) worst case</td>
                                <td>Poor (thread divergence)</td>
                                <td>Must enumerate to count</td>
                            </tr>
                            <tr>
                                <td>Linear Sweep</td>
                                <td>Sorted arrays + auxiliary</td>
                                <td>O(N log N)</td>
                                <td>Limited</td>
                                <td>Sweep invariant overhead</td>
                            </tr>
                            <tr>
                                <td>Interval Trees</td>
                                <td>Balanced BST</td>
                                <td>O(N log N + K)</td>
                                <td>Difficult</td>
                                <td>Complex implementation</td>
                            </tr>
                            <tr>
                                <td>Naive Binary Search</td>
                                <td>Sorted array</td>
                                <td>O(N²) worst case</td>
                                <td>Good</td>
                                <td>Contained intervals</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section id="theory" class="section">
                <h2>3. BITS Algorithm Theory</h2>

                <h3>Core Innovation: Exclusion-Based Counting</h3>
                <div class="highlight">
                    Instead of finding intervals that <strong>do</strong> intersect (inclusion), BITS finds intervals that <strong>cannot</strong> intersect (exclusion) and infers the intersection count from the remainder.
                </div>

                <h3>Mathematical Foundation</h3>
                <p>For a query interval <code>aᵢ</code> and database <code>B</code>, we define:</p>

                <div class="formula">
                    <p><strong>Left Set (L):</strong> Intervals ending before query starts</p>
                    $$L(B, a_i) = \{b \in B \mid b.end < a_i.start\}$$

                    <p><strong>Right Set (R):</strong> Intervals starting after query ends</p>
                    $$R(B, a_i) = \{b \in B \mid b.start > a_i.end\}$$

                    <p><strong>Intersection Set (I):</strong> All intervals except those in L or R</p>
                    $$I(B, a_i) = B \setminus (L(B, a_i) \cup R(B, a_i))$$
                </div>

                <div class="svg-container">
                    <svg width="900" height="450" viewBox="0 0 900 450">
                        <defs>
                            <linearGradient id="leftGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="rightGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="intersectGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="queryGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">BITS Exclusion-Based Approach</text>

                        <!-- Timeline -->
                        <line x1="100" y1="80" x2="800" y2="80" stroke="#34495e" stroke-width="3"/>
                        <text x="100" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">0</text>
                        <text x="200" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">10</text>
                        <text x="300" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">20</text>
                        <text x="400" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">30</text>
                        <text x="500" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">40</text>
                        <text x="600" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">50</text>
                        <text x="700" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">60</text>
                        <text x="800" y="75" text-anchor="middle" fill="#7f8c8d" font-size="12">70</text>

                        <!-- Query interval -->
                        <rect x="300" y="100" width="200" height="25" fill="url(#queryGrad)" rx="3"/>
                        <text x="400" y="118" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Query [20, 40]</text>

                        <!-- Left set intervals (end before query starts) -->
                        <text x="50" y="160" fill="#e74c3c" font-size="14" font-weight="bold">Left Set L (end &lt; 20):</text>

                        <rect x="120" y="170" width="60" height="20" fill="url(#leftGrad)" rx="3"/>
                        <text x="150" y="185" text-anchor="middle" fill="white" font-size="10">[2, 8]</text>

                        <rect x="180" y="195" width="80" height="20" fill="url(#leftGrad)" rx="3"/>
                        <text x="220" y="210" text-anchor="middle" fill="white" font-size="10">[5, 15]</text>

                        <rect x="200" y="220" width="60" height="20" fill="url(#leftGrad)" rx="3"/>
                        <text x="230" y="235" text-anchor="middle" fill="white" font-size="10">[12, 18]</text>

                        <!-- Right set intervals (start after query ends) -->
                        <text x="550" y="160" fill="#9b59b6" font-size="14" font-weight="bold">Right Set R (start &gt; 40):</text>

                        <rect x="620" y="170" width="80" height="20" fill="url(#rightGrad)" rx="3"/>
                        <text x="660" y="185" text-anchor="middle" fill="white" font-size="10">[45, 55]</text>

                        <rect x="650" y="195" width="60" height="20" fill="url(#rightGrad)" rx="3"/>
                        <text x="680" y="210" text-anchor="middle" fill="white" font-size="10">[50, 60]</text>

                        <rect x="680" y="220" width="80" height="20" fill="url(#rightGrad)" rx="3"/>
                        <text x="720" y="235" text-anchor="middle" fill="white" font-size="10">[55, 65]</text>

                        <!-- Intersection set intervals -->
                        <text x="300" y="160" fill="#27ae60" font-size="14" font-weight="bold">Intersection Set I:</text>

                        <rect x="280" y="170" width="100" height="20" fill="url(#intersectGrad)" rx="3"/>
                        <text x="330" y="185" text-anchor="middle" fill="white" font-size="10">[15, 35]</text>

                        <rect x="320" y="195" width="80" height="20" fill="url(#intersectGrad)" rx="3"/>
                        <text x="360" y="210" text-anchor="middle" fill="white" font-size="10">[25, 45]</text>

                        <rect x="350" y="220" width="120" height="20" fill="url(#intersectGrad)" rx="3"/>
                        <text x="410" y="235" text-anchor="middle" fill="white" font-size="10">[30, 50]</text>

                        <!-- Binary search illustration -->
                        <text x="450" y="280" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">BITS Binary Search Strategy</text>

                        <!-- Step 1: Search in ends -->
                        <text x="50" y="310" fill="#2c3e50" font-size="14" font-weight="bold">Step 1: Binary search query.start in sorted ends</text>
                        <rect x="70" y="320" width="300" height="25" fill="#ecf0f1" stroke="#bdc3c7" rx="3"/>
                        <text x="220" y="337" text-anchor="middle" fill="#2c3e50" font-size="12">Sorted Ends: [8, 15, 18, 35, 45, 50, 55, 60, 65]</text>

                        <text x="70" y="360" fill="#e74c3c" font-size="12">Binary search for 20 → finds position 3 → |L| = 3</text>

                        <!-- Step 2: Search in starts -->
                        <text x="50" y="385" fill="#2c3e50" font-size="14" font-weight="bold">Step 2: Binary search query.end in sorted starts</text>
                        <rect x="70" y="395" width="300" height="25" fill="#ecf0f1" stroke="#bdc3c7" rx="3"/>
                        <text x="220" y="412" text-anchor="middle" fill="#2c3e50" font-size="12">Sorted Starts: [2, 5, 12, 15, 25, 30, 45, 50, 55]</text>

                        <text x="70" y="435" fill="#9b59b6" font-size="12">Binary search for 40 → finds position 6 → |R| = 9 - 6 = 3</text>

                        <!-- Final calculation -->
                        <div class="algorithm-box" style="position: absolute; left: 500px; top: 320px; width: 350px;">
                            <div style="text-align: center; font-size: 14px; font-weight: bold; margin-bottom: 10px;">BITS Calculation:</div>
                            <div style="font-size: 12px;">
                                |B| = 9 (total intervals)<br/>
                                |L| = 3 (left exclusions)<br/>
                                |R| = 3 (right exclusions)<br/>
                                <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 8px 0;"/>
                                |I| = |B| - |L| - |R|<br/>
                                |I| = 9 - 3 - 3 = 3<br/>
                                <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 8px 0;"/>
                                <strong>Result: 3 intersections</strong>
                            </div>
                        </div>
                    </svg>
                </div>

                <h3>Key Advantages of Exclusion Approach</h3>
                <div class="info">
                    <ul>
                        <li><strong>No Enumeration Required:</strong> Count intersections without listing them</li>
                        <li><strong>Handles Contained Intervals:</strong> Exclusion sets are well-defined regardless of nesting</li>
                        <li><strong>Predictable Performance:</strong> Always exactly 2 binary searches per query</li>
                        <li><strong>Parallel Friendly:</strong> Each query is completely independent</li>
                        <li><strong>Memory Efficient:</strong> Only requires sorted arrays of coordinates</li>
                    </ul>
                </div>

                <h3>Time Complexity Analysis</h3>
                <div class="formula">
                    <p><strong>Preprocessing:</strong> Sort database intervals</p>
                    $$O(|B| \log |B|)$$

                    <p><strong>Per Query:</strong> Two binary searches</p>
                    $$O(\log |B|)$$

                    <p><strong>Total for all queries:</strong></p>
                    $$O(|B| \log |B| + |A| \log |B|) = O((|A| + |B|) \log |B|)$$

                    <p><strong>When |A| ≈ |B| = N:</strong></p>
                    $$O(N \log N)$$
                </div>

                <div class="performance-box">
                    <strong>Optimality:</strong> The O(N log N) complexity is theoretically optimal for the interval intersection counting problem, proven by reduction to the element uniqueness problem.
                </div>
            </section>

            <section id="implementation" class="section">
                <h2>4. Implementation Analysis</h2>

                <h3>BITS Algorithm Pseudocode</h3>
                <div class="algorithm-box">
                    <strong>Algorithm 1: Single Interval Intersection Counter</strong><br/>
                    <strong>Input:</strong> Sorted starts BS, sorted ends BE, query interval a<br/>
                    <strong>Output:</strong> Number of intervals c intersecting a<br/><br/>

                    <strong>Function ICOUNT(BS, BE, a):</strong><br/>
                    &nbsp;&nbsp;first ← BSEARCH(BS, a.end)<br/>
                    &nbsp;&nbsp;last ← BSEARCH(BE, a.start)<br/>
                    &nbsp;&nbsp;c ← first - last<br/>
                    &nbsp;&nbsp;return c<br/><br/>

                    <strong>Algorithm 2: Complete Intersection Counter</strong><br/>
                    <strong>Function COUNTER(A, B):</strong><br/>
                    &nbsp;&nbsp;BS ← sorted array of B start coordinates<br/>
                    &nbsp;&nbsp;BE ← sorted array of B end coordinates<br/>
                    &nbsp;&nbsp;c ← 0<br/>
                    &nbsp;&nbsp;for each aᵢ in A:<br/>
                    &nbsp;&nbsp;&nbsp;&nbsp;c ← c + ICOUNT(BS, BE, aᵢ)<br/>
                    &nbsp;&nbsp;return c
                </div>

                <h3>Rust Implementation: Lapper Structure</h3>
                <p>The Rust implementation uses a structure called <code>Lapper</code> that implements the BITS algorithm:</p>

                <div class="rust-code">
#[derive(Debug, Clone)]
pub struct Lapper&lt;I, T&gt; {
    /// List of intervals
    pub intervals: Vec&lt;Interval&lt;I, T&gt;&gt;,
    /// Sorted list of start positions
    starts: Vec&lt;I&gt;,
    /// Sorted list of end positions
    stops: Vec&lt;I&gt;,
    /// The length of the longest interval
    max_len: I,
    /// Whether overlaps have been merged
    pub overlaps_merged: bool,
}

#[derive(Debug, Clone)]
pub struct Interval&lt;I, T&gt; {
    pub start: I,
    pub stop: I,
    pub val: T,
}
                </div>

                <h3>Core BITS Implementation: The count() Method</h3>
                <div class="rust-code">
/// Count all intervals that overlap start .. stop using BITS algorithm
#[inline]
pub fn count(&amp;self, start: I, stop: I) -&gt; usize {
    let len = self.intervals.len();

    // Binary search in sorted stops for query start
    // Plus one to account for half-openness of intervals
    let first = Self::bsearch_seq(start + one::&lt;I&gt;(), &amp;self.stops);

    // Binary search in sorted starts for query stop
    let last = Self::bsearch_seq(stop, &amp;self.starts);

    // Calculate exclusions
    let num_cant_after = len - last;  // Right exclusions

    // Return intersection count: total - left_exclusions - right_exclusions
    len - first - num_cant_after
}
                </div>

                <h3>Binary Search Implementation</h3>
                <div class="rust-code">
#[inline]
pub fn bsearch_seq_ref&lt;K&gt;(key: &amp;K, elems: &amp;[K]) -&gt; usize
where
    K: PartialEq + PartialOrd,
{
    if elems.is_empty() || elems[0] &gt;= *key {
        return 0;
    } else if elems[elems.len() - 1] &lt; *key {
        return elems.len();
    }

    let mut cursor = 0;
    let mut length = elems.len();

    while length &gt; 1 {
        let half = length &gt;&gt; 1;
        length -= half;
        cursor += (usize::from(elems[cursor + half - 1] &lt; *key)) * half;
    }
    cursor
}
                </div>

                <h3>Interval Construction and Optimization</h3>
                <div class="rust-code">
pub fn new(mut intervals: Vec&lt;Interval&lt;I, T&gt;&gt;) -&gt; Self {
    // Sort intervals by start position
    intervals.sort();

    // Extract and sort start/stop coordinates separately
    let (mut starts, mut stops): (Vec&lt;_&gt;, Vec&lt;_&gt; =
        intervals.iter().map(|x| (x.start, x.stop)).unzip();
    starts.sort();
    stops.sort();

    // Calculate maximum interval length for optimization
    let mut max_len = zero::&lt;I&gt;();
    for interval in intervals.iter() {
        let i_len = interval.stop.checked_sub(&amp;interval.start)
                             .unwrap_or_else(zero::&lt;I&gt;);
        if i_len &gt; max_len {
            max_len = i_len;
        }
    }

    Lapper {
        intervals,
        starts,
        stops,
        max_len,
        overlaps_merged: false,
    }
}
                </div>

                <h3>Step-by-Step BITS Execution</h3>
                <div class="svg-container">
                    <svg width="900" height="500" viewBox="0 0 900 500">
                        <defs>
                            <linearGradient id="stepGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">BITS Algorithm Execution Steps</text>

                        <!-- Step 1: Data Preparation -->
                        <rect x="50" y="50" width="800" height="60" fill="#ecf0f1" stroke="#bdc3c7" rx="5"/>
                        <text x="60" y="70" fill="#2c3e50" font-size="14" font-weight="bold">Step 1: Data Preparation</text>
                        <text x="60" y="90" fill="#34495e" font-size="12">• Sort intervals by start coordinate</text>
                        <text x="60" y="105" fill="#34495e" font-size="12">• Extract and sort start coordinates: [5, 10, 15, 25, 30, 45, 50, 55]</text>

                        <!-- Step 2: Sort ends -->
                        <rect x="50" y="120" width="800" height="45" fill="#e8f5e8" stroke="#27ae60" rx="5"/>
                        <text x="60" y="140" fill="#2c3e50" font-size="14" font-weight="bold">Step 2: Sort End Coordinates</text>
                        <text x="60" y="155" fill="#34495e" font-size="12">• Extract and sort end coordinates: [8, 15, 18, 35, 40, 50, 60, 65]</text>

                        <!-- Step 3: Query processing -->
                        <rect x="50" y="175" width="800" height="80" fill="#fff3cd" stroke="#f39c12" rx="5"/>
                        <text x="60" y="195" fill="#2c3e50" font-size="14" font-weight="bold">Step 3: Process Query [20, 40]</text>
                        <text x="60" y="215" fill="#34495e" font-size="12">• Binary search query.start (20) in sorted ends: position = 3</text>
                        <text x="60" y="230" fill="#34495e" font-size="12">• Binary search query.end (40) in sorted starts: position = 5</text>
                        <text x="60" y="245" fill="#34495e" font-size="12">• Calculate: count = total_intervals - left_exclusions - right_exclusions</text>

                        <!-- Step 4: Result -->
                        <rect x="50" y="265" width="800" height="45" fill="#d1ecf1" stroke="#17a2b8" rx="5"/>
                        <text x="60" y="285" fill="#2c3e50" font-size="14" font-weight="bold">Step 4: Calculate Result</text>
                        <text x="60" y="300" fill="#34495e" font-size="12">• Result: 8 - 3 - 3 = 2 intersecting intervals</text>

                        <!-- Visual representation -->
                        <text x="450" y="340" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Binary Search Visualization</text>

                        <!-- Sorted ends array -->
                        <text x="50" y="370" fill="#2c3e50" font-size="12" font-weight="bold">Sorted Ends:</text>
                        <rect x="150" y="355" width="40" height="25" fill="#e74c3c" rx="3"/>
                        <text x="170" y="372" text-anchor="middle" fill="white" font-size="10">8</text>
                        <rect x="200" y="355" width="40" height="25" fill="#e74c3c" rx="3"/>
                        <text x="220" y="372" text-anchor="middle" fill="white" font-size="10">15</text>
                        <rect x="250" y="355" width="40" height="25" fill="#e74c3c" rx="3"/>
                        <text x="270" y="372" text-anchor="middle" fill="white" font-size="10">18</text>
                        <rect x="300" y="355" width="40" height="25" fill="#27ae60" rx="3"/>
                        <text x="320" y="372" text-anchor="middle" fill="white" font-size="10">35</text>
                        <rect x="350" y="355" width="40" height="25" fill="#27ae60" rx="3"/>
                        <text x="370" y="372" text-anchor="middle" fill="white" font-size="10">40</text>

                        <text x="420" y="372" fill="#e74c3c" font-size="12">← Search for 20, find position 3</text>

                        <!-- Sorted starts array -->
                        <text x="50" y="410" fill="#2c3e50" font-size="12" font-weight="bold">Sorted Starts:</text>
                        <rect x="150" y="395" width="40" height="25" fill="#27ae60" rx="3"/>
                        <text x="170" y="412" text-anchor="middle" fill="white" font-size="10">5</text>
                        <rect x="200" y="395" width="40" height="25" fill="#27ae60" rx="3"/>
                        <text x="220" y="412" text-anchor="middle" fill="white" font-size="10">10</text>
                        <rect x="250" y="395" width="40" height="25" fill="#27ae60" rx="3"/>
                        <text x="270" y="412" text-anchor="middle" fill="white" font-size="10">15</text>
                        <rect x="300" y="395" width="40" height="25" fill="#27ae60" rx="3"/>
                        <text x="320" y="412" text-anchor="middle" fill="white" font-size="10">25</text>
                        <rect x="350" y="395" width="40" height="25" fill="#27ae60" rx="3"/>
                        <text x="370" y="412" text-anchor="middle" fill="white" font-size="10">30</text>
                        <rect x="400" y="395" width="40" height="25" fill="#9b59b6" rx="3"/>
                        <text x="420" y="412" text-anchor="middle" fill="white" font-size="10">45</text>
                        <rect x="450" y="395" width="40" height="25" fill="#9b59b6" rx="3"/>
                        <text x="470" y="412" text-anchor="middle" fill="white" font-size="10">50</text>

                        <text x="520" y="412" fill="#9b59b6" font-size="12">← Search for 40, find position 5</text>

                        <!-- Calculation -->
                        <rect x="600" y="440" width="250" height="50" fill="url(#stepGrad)" rx="5"/>
                        <text x="725" y="460" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Final Calculation:</text>
                        <text x="725" y="475" text-anchor="middle" fill="white" font-size="11">7 total - 3 left - 2 right = 2 intersections</text>
                    </svg>
                </div>
            </section>

            <section id="performance" class="section">
                <h2>5. Performance Analysis</h2>

                <h3>Theoretical Complexity</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Operation</th>
                                <th>Time Complexity</th>
                                <th>Space Complexity</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Preprocessing</td>
                                <td>O(|B| log |B|)</td>
                                <td>O(|B|)</td>
                                <td>Sort database intervals</td>
                            </tr>
                            <tr>
                                <td>Single Query</td>
                                <td>O(log |B|)</td>
                                <td>O(1)</td>
                                <td>Two binary searches</td>
                            </tr>
                            <tr>
                                <td>All Queries</td>
                                <td>O(|A| log |B|)</td>
                                <td>O(1)</td>
                                <td>Independent queries</td>
                            </tr>
                            <tr>
                                <td>Total</td>
                                <td>O((|A| + |B|) log |B|)</td>
                                <td>O(|B|)</td>
                                <td>Optimal for counting</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Empirical Performance Results</h3>
                <p>Based on the original BITS paper benchmarks using genomic datasets:</p>

                <div class="svg-container">
                    <svg width="900" height="400" viewBox="0 0 900 400">
                        <defs>
                            <linearGradient id="bitsBar" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="bedBar" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="ucscBar" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Performance Comparison: Runtime (microseconds)</text>

                        <!-- Y-axis -->
                        <line x1="80" y1="50" x2="80" y2="350" stroke="#34495e" stroke-width="2"/>
                        <text x="75" y="55" text-anchor="end" fill="#7f8c8d" font-size="10">10⁹</text>
                        <text x="75" y="100" text-anchor="end" fill="#7f8c8d" font-size="10">10⁸</text>
                        <text x="75" y="150" text-anchor="end" fill="#7f8c8d" font-size="10">10⁷</text>
                        <text x="75" y="200" text-anchor="end" fill="#7f8c8d" font-size="10">10⁶</text>
                        <text x="75" y="250" text-anchor="end" fill="#7f8c8d" font-size="10">10⁵</text>
                        <text x="75" y="300" text-anchor="end" fill="#7f8c8d" font-size="10">10⁴</text>
                        <text x="75" y="345" text-anchor="end" fill="#7f8c8d" font-size="10">10³</text>

                        <!-- X-axis -->
                        <line x1="80" y1="350" x2="850" y2="350" stroke="#34495e" stroke-width="2"/>

                        <!-- Test scenarios -->
                        <text x="180" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">Biased Distribution</text>
                        <text x="350" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">Uniform Distribution</text>
                        <text x="520" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">Different Distributions</text>
                        <text x="690" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">Large Dataset (10M)</text>

                        <!-- 1M intervals results -->
                        <text x="450" y="390" text-anchor="middle" fill="#7f8c8d" font-size="11">1 Million Intervals</text>

                        <!-- Biased distribution bars -->
                        <rect x="120" y="320" width="30" height="30" fill="url(#bitsBar)" rx="2"/>
                        <rect x="155" y="280" width="30" height="70" fill="url(#ucscBar)" rx="2"/>
                        <rect x="190" y="100" width="30" height="250" fill="url(#bedBar)" rx="2"/>

                        <!-- Performance labels -->
                        <text x="135" y="315" text-anchor="middle" fill="#27ae60" font-size="9" font-weight="bold">BITS</text>
                        <text x="170" y="275" text-anchor="middle" fill="#f39c12" font-size="9" font-weight="bold">1.7x</text>
                        <text x="205" y="95" text-anchor="middle" fill="#e74c3c" font-size="9" font-weight="bold">21.6x</text>

                        <!-- Uniform distribution bars -->
                        <rect x="290" y="325" width="30" height="25" fill="url(#bitsBar)" rx="2"/>
                        <rect x="325" y="300" width="30" height="50" fill="url(#ucscBar)" rx="2"/>
                        <rect x="360" y="120" width="30" height="230" fill="url(#bedBar)" rx="2"/>

                        <text x="305" y="320" text-anchor="middle" fill="#27ae60" font-size="9" font-weight="bold">BITS</text>
                        <text x="340" y="295" text-anchor="middle" fill="#f39c12" font-size="9" font-weight="bold">1.2x</text>
                        <text x="375" y="115" text-anchor="middle" fill="#e74c3c" font-size="9" font-weight="bold">21.7x</text>

                        <!-- Different distributions bars -->
                        <rect x="460" y="330" width="30" height="20" fill="url(#bitsBar)" rx="2"/>
                        <rect x="495" y="310" width="30" height="40" fill="url(#ucscBar)" rx="2"/>
                        <rect x="530" y="80" width="30" height="270" fill="url(#bedBar)" rx="2"/>

                        <text x="475" y="325" text-anchor="middle" fill="#27ae60" font-size="9" font-weight="bold">BITS</text>
                        <text x="510" y="305" text-anchor="middle" fill="#f39c12" font-size="9" font-weight="bold">1.3x</text>
                        <text x="545" y="75" text-anchor="middle" fill="#e74c3c" font-size="9" font-weight="bold">27.9x</text>

                        <!-- Large dataset bars -->
                        <rect x="630" y="335" width="30" height="15" fill="url(#bitsBar)" rx="2"/>
                        <rect x="665" y="250" width="30" height="100" fill="url(#ucscBar)" rx="2"/>
                        <rect x="700" y="60" width="30" height="290" fill="url(#bedBar)" rx="2"/>

                        <text x="645" y="330" text-anchor="middle" fill="#27ae60" font-size="9" font-weight="bold">BITS</text>
                        <text x="680" y="245" text-anchor="middle" fill="#f39c12" font-size="9" font-weight="bold">5.2x</text>
                        <text x="715" y="55" text-anchor="middle" fill="#e74c3c" font-size="9" font-weight="bold">28.7x</text>

                        <!-- Legend -->
                        <rect x="750" y="80" width="15" height="15" fill="url(#bitsBar)"/>
                        <text x="775" y="92" fill="#2c3e50" font-size="12">BITS</text>

                        <rect x="750" y="105" width="15" height="15" fill="url(#ucscBar)"/>
                        <text x="775" y="117" fill="#2c3e50" font-size="12">UCSC</text>

                        <rect x="750" y="130" width="15" height="15" fill="url(#bedBar)"/>
                        <text x="775" y="142" fill="#2c3e50" font-size="12">BEDTools</text>

                        <text x="775" y="165" fill="#7f8c8d" font-size="10">Numbers show</text>
                        <text x="775" y="178" fill="#7f8c8d" font-size="10">speedup vs BITS</text>
                    </svg>
                </div>

                <h3>Parallel Performance on GPU</h3>
                <div class="performance-box">
                    <strong>BITS-CUDA Performance:</strong>
                    <ul>
                        <li><strong>Single intersection:</strong> 4x faster than sequential BITS</li>
                        <li><strong>10,000 MC rounds:</strong> 267x faster than sequential BITS</li>
                        <li><strong>Large-scale analysis:</strong> 3,414x faster than sequential UCSC</li>
                        <li><strong>Real-world example:</strong> 25,281 pairwise comparisons completed in 6 days vs 112 traditional processors</li>
                    </ul>
                </div>

                <h3>Why BITS Excels</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Advantage</th>
                                <th>Traditional Methods</th>
                                <th>BITS</th>
                                <th>Impact</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Data Structure</td>
                                <td>Complex trees/bins</td>
                                <td>Simple sorted arrays</td>
                                <td>Lower memory overhead</td>
                            </tr>
                            <tr>
                                <td>Counting Method</td>
                                <td>Enumerate then count</td>
                                <td>Direct counting</td>
                                <td>No enumeration overhead</td>
                            </tr>
                            <tr>
                                <td>Distribution Sensitivity</td>
                                <td>Performance varies</td>
                                <td>Consistent performance</td>
                                <td>Predictable runtime</td>
                            </tr>
                            <tr>
                                <td>Parallelization</td>
                                <td>Thread divergence</td>
                                <td>Independent queries</td>
                                <td>Excellent GPU performance</td>
                            </tr>
                            <tr>
                                <td>Cache Efficiency</td>
                                <td>Random access patterns</td>
                                <td>Sequential array access</td>
                                <td>Better cache utilization</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Monte Carlo Simulation Performance</h3>
                <div class="formula">
                    <p><strong>Speedup for MC simulations:</strong></p>
                    $$\text{Speedup} = \frac{\text{Setup time saved} \times \text{Number of rounds}}{\text{Base execution time}}$$

                    <p><strong>BITS MC advantage:</strong> 6x speedup per round after first setup</p>
                </div>

                <div class="info">
                    <strong>Monte Carlo Efficiency:</strong> BITS excels at Monte Carlo simulations because:
                    <ul>
                        <li>Setup cost is amortized over many rounds</li>
                        <li>Random interval generation is faster with sorted arrays</li>
                        <li>Parallel random number generation scales well</li>
                        <li>No communication overhead between threads</li>
                    </ul>
                </div>
            </section>

            <section id="applications" class="section">
                <h2>6. Practical Applications</h2>

                <h3>Genomic Data Analysis</h3>
                <div class="svg-container">
                    <svg width="900" height="350" viewBox="0 0 900 350">
                        <defs>
                            <linearGradient id="geneGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="readGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="peakGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">BITS in Genomic Analysis Workflows</text>

                        <!-- Chromosome representation -->
                        <line x1="100" y1="80" x2="800" y2="80" stroke="#34495e" stroke-width="4"/>
                        <text x="450" y="70" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Chromosome 1 (example region)</text>

                        <!-- Genes -->
                        <text x="50" y="120" fill="#3498db" font-size="12" font-weight="bold">Genes:</text>
                        <rect x="150" y="100" width="120" height="20" fill="url(#geneGrad)" rx="3"/>
                        <text x="210" y="115" text-anchor="middle" fill="white" font-size="10">Gene A</text>

                        <rect x="350" y="100" width="80" height="20" fill="url(#geneGrad)" rx="3"/>
                        <text x="390" y="115" text-anchor="middle" fill="white" font-size="10">Gene B</text>

                        <rect x="500" y="100" width="150" height="20" fill="url(#geneGrad)" rx="3"/>
                        <text x="575" y="115" text-anchor="middle" fill="white" font-size="10">Gene C</text>

                        <!-- RNA-seq reads -->
                        <text x="50" y="160" fill="#e74c3c" font-size="12" font-weight="bold">RNA-seq Reads:</text>
                        <rect x="140" y="140" width="60" height="15" fill="url(#readGrad)" rx="2"/>
                        <rect x="180" y="160" width="60" height="15" fill="url(#readGrad)" rx="2"/>
                        <rect x="220" y="140" width="60" height="15" fill="url(#readGrad)" rx="2"/>
                        <rect x="340" y="160" width="60" height="15" fill="url(#readGrad)" rx="2"/>
                        <rect x="380" y="140" width="60" height="15" fill="url(#readGrad)" rx="2"/>
                        <rect x="520" y="160" width="60" height="15" fill="url(#readGrad)" rx="2"/>
                        <rect x="560" y="140" width="60" height="15" fill="url(#readGrad)" rx="2"/>
                        <rect x="600" y="160" width="60" height="15" fill="url(#readGrad)" rx="2"/>

                        <!-- ChIP-seq peaks -->
                        <text x="50" y="210" fill="#f39c12" font-size="12" font-weight="bold">ChIP-seq Peaks:</text>
                        <rect x="160" y="190" width="40" height="15" fill="url(#peakGrad)" rx="2"/>
                        <rect x="360" y="190" width="30" height="15" fill="url(#peakGrad)" rx="2"/>
                        <rect x="540" y="190" width="50" height="15" fill="url(#peakGrad)" rx="2"/>

                        <!-- BITS analysis boxes -->
                        <rect x="100" y="240" width="200" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="200" y="260" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">RNA-seq Quantification</text>
                        <text x="200" y="280" text-anchor="middle" fill="#34495e" font-size="10">Count reads per gene</text>
                        <text x="200" y="295" text-anchor="middle" fill="#34495e" font-size="10">BITS: O(R log G)</text>
                        <text x="200" y="310" text-anchor="middle" fill="#27ae60" font-size="10">R=reads, G=genes</text>

                        <rect x="350" y="240" width="200" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="5"/>
                        <text x="450" y="260" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Peak-Gene Association</text>
                        <text x="450" y="280" text-anchor="middle" fill="#34495e" font-size="10">Find peaks near genes</text>
                        <text x="450" y="295" text-anchor="middle" fill="#34495e" font-size="10">BITS: O(P log G)</text>
                        <text x="450" y="310" text-anchor="middle" fill="#f39c12" font-size="10">P=peaks, G=genes</text>

                        <rect x="600" y="240" width="200" height="80" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="5"/>
                        <text x="700" y="260" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Variant Impact</text>
                        <text x="700" y="280" text-anchor="middle" fill="#34495e" font-size="10">Variants in genes</text>
                        <text x="700" y="295" text-anchor="middle" fill="#34495e" font-size="10">BITS: O(V log G)</text>
                        <text x="700" y="310" text-anchor="middle" fill="#17a2b8" font-size="10">V=variants, G=genes</text>
                    </svg>
                </div>

                <h3>Real-World Use Cases</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Application</th>
                                <th>Query Set</th>
                                <th>Database Set</th>
                                <th>Typical Scale</th>
                                <th>BITS Advantage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>RNA-seq Quantification</td>
                                <td>Sequencing reads</td>
                                <td>Gene annotations</td>
                                <td>10⁹ reads vs 10⁴ genes</td>
                                <td>Handles massive read counts</td>
                            </tr>
                            <tr>
                                <td>ChIP-seq Analysis</td>
                                <td>TF binding peaks</td>
                                <td>Promoter regions</td>
                                <td>10⁵ peaks vs 10⁴ promoters</td>
                                <td>Fast peak-gene association</td>
                            </tr>
                            <tr>
                                <td>Variant Annotation</td>
                                <td>SNPs/Indels</td>
                                <td>Functional elements</td>
                                <td>10⁷ variants vs 10⁶ elements</td>
                                <td>Efficient variant impact</td>
                            </tr>
                            <tr>
                                <td>Copy Number Analysis</td>
                                <td>CNV segments</td>
                                <td>Gene boundaries</td>
                                <td>10³ CNVs vs 10⁴ genes</td>
                                <td>Precise overlap counting</td>
                            </tr>
                            <tr>
                                <td>Regulatory Analysis</td>
                                <td>Enhancers</td>
                                <td>Target genes</td>
                                <td>10⁵ enhancers vs 10⁴ genes</td>
                                <td>Distance-based queries</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Monte Carlo Simulations in Genomics</h3>
                <div class="highlight">
                    <strong>Statistical Significance Testing:</strong> BITS excels at Monte Carlo simulations for determining if observed overlaps are statistically significant compared to random expectations.
                </div>

                <div class="algorithm-box">
                    <strong>Monte Carlo Workflow with BITS:</strong><br/>
                    1. Observe actual intersection count between datasets A and B<br/>
                    2. Generate N random permutations of dataset A<br/>
                    3. For each permutation, count intersections with B using BITS<br/>
                    4. Calculate p-value: fraction of random counts ≥ observed count<br/>
                    5. BITS advantage: Setup cost amortized over many rounds
                </div>
            </section>

            <section id="comparison" class="section">
                <h2>7. Algorithm Comparisons</h2>

                <h3>Comprehensive Method Comparison</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Method</th>
                                <th>Time Complexity</th>
                                <th>Space Complexity</th>
                                <th>Parallelization</th>
                                <th>Implementation</th>
                                <th>Best Use Case</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>BITS</strong></td>
                                <td>O(N log N)</td>
                                <td>O(N)</td>
                                <td>Excellent</td>
                                <td>Simple</td>
                                <td>Counting, large-scale</td>
                            </tr>
                            <tr>
                                <td>Interval Trees</td>
                                <td>O(N log N + K)</td>
                                <td>O(N)</td>
                                <td>Difficult</td>
                                <td>Complex</td>
                                <td>Enumeration, small K</td>
                            </tr>
                            <tr>
                                <td>R-trees (UCSC)</td>
                                <td>O(N²) worst</td>
                                <td>O(N)</td>
                                <td>Poor</td>
                                <td>Complex</td>
                                <td>Spatial databases</td>
                            </tr>
                            <tr>
                                <td>Linear Sweep</td>
                                <td>O(N log N + K)</td>
                                <td>O(N)</td>
                                <td>Limited</td>
                                <td>Moderate</td>
                                <td>Enumeration</td>
                            </tr>
                            <tr>
                                <td>Naive Binary Search</td>
                                <td>O(N²) worst</td>
                                <td>O(N)</td>
                                <td>Good</td>
                                <td>Simple</td>
                                <td>Small datasets</td>
                            </tr>
                            <tr>
                                <td>BEDTools</td>
                                <td>O(N²) worst</td>
                                <td>O(N)</td>
                                <td>None</td>
                                <td>Feature-rich</td>
                                <td>General genomics</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>When to Choose BITS</h3>
                <div class="info">
                    <strong>BITS is optimal when:</strong>
                    <ul>
                        <li>You need to <strong>count</strong> intersections rather than enumerate them</li>
                        <li>Working with <strong>large datasets</strong> (millions+ intervals)</li>
                        <li>Performing <strong>many queries</strong> against the same database</li>
                        <li>Running <strong>Monte Carlo simulations</strong></li>
                        <li>Need <strong>predictable performance</strong> regardless of data distribution</li>
                        <li>Want to leverage <strong>parallel computing</strong> (GPU/multi-core)</li>
                        <li>Memory efficiency is important</li>
                    </ul>
                </div>

                <h3>When to Choose Alternatives</h3>
                <div class="warning">
                    <strong>Consider other methods when:</strong>
                    <ul>
                        <li><strong>Interval Trees:</strong> Need to enumerate all intersections efficiently</li>
                        <li><strong>BEDTools:</strong> Need rich genomic operations beyond intersection</li>
                        <li><strong>Linear Sweep:</strong> One-time analysis with complex intersection logic</li>
                        <li><strong>R-trees:</strong> Working with 2D/3D spatial data</li>
                    </ul>
                </div>

                <h3>Future Directions</h3>
                <div class="highlight">
                    <strong>BITS Extensions and Improvements:</strong>
                    <ul>
                        <li><strong>BITS-Tree:</strong> Hierarchical BITS for very large datasets</li>
                        <li><strong>Approximate BITS:</strong> Trade accuracy for speed in massive datasets</li>
                        <li><strong>Streaming BITS:</strong> Handle dynamic interval sets</li>
                        <li><strong>Multi-dimensional BITS:</strong> Extend to 2D/3D intervals</li>
                        <li><strong>Distributed BITS:</strong> Scale across multiple machines</li>
                    </ul>
                </div>

                <div class="performance-box">
                    <strong>Conclusion:</strong> BITS represents a paradigm shift in interval intersection algorithms, moving from enumeration-based to exclusion-based counting. Its simplicity, optimal complexity, and excellent parallelization properties make it the method of choice for large-scale genomic data analysis.
                </div>
            </section>
        </div>
    </div>
</body>
</html>
    </div>
</body>
</html>
