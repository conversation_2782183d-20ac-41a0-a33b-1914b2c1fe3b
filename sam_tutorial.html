<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAM File Format Tutorial</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 0.5rem 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 5px solid #3498db;
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 1.5rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 4px solid #e74c3c;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 1rem 0;
            border: 1px solid #34495e;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .nav {
            background: #34495e;
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
        }
        
        .nav li {
            margin-right: 2rem;
        }
        
        .nav a {
            color: #ecf0f1;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav a:hover {
            color: #3498db;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .formula {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .warning {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 4px solid #e17055;
        }
        
        .info {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SAM File Format Tutorial</h1>
            <p>Comprehensive Guide to Sequence Alignment/Map Format</p>
        </div>
        
        <div class="nav">
            <ul>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#structure">File Structure</a></li>
                <li><a href="#header">Header Section</a></li>
                <li><a href="#alignment">Alignment Section</a></li>
                <li><a href="#examples">Examples</a></li>
                <li><a href="#best-practices">Best Practices</a></li>
            </ul>
        </div>
        
        <div class="content">
            <section id="overview" class="section">
                <h2>1. Overview of SAM Format</h2>

                <div class="highlight">
                    <strong>SAM</strong> stands for <strong>Sequence Alignment/Map format</strong>. It is a TAB-delimited text format consisting of a header section (optional) and an alignment section.
                </div>

                <h3>Key Characteristics</h3>
                <ul>
                    <li><strong>Text-based format</strong>: Human-readable and easy to parse</li>
                    <li><strong>TAB-delimited</strong>: Fields separated by tab characters</li>
                    <li><strong>7-bit US-ASCII</strong>: Standard character encoding (UTF-8 for specific fields)</li>
                    <li><strong>Version 1.6</strong>: Current specification version</li>
                </ul>

                <div class="svg-container">
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- SAM File Structure Overview -->
                        <defs>
                            <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="alignGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                    refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
                            </marker>
                        </defs>

                        <!-- File representation -->
                        <rect x="50" y="50" width="700" height="200" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10"/>

                        <!-- Header section -->
                        <rect x="70" y="70" width="660" height="60" fill="url(#headerGrad)" rx="5"/>
                        <text x="400" y="105" text-anchor="middle" fill="white" font-size="18" font-weight="bold">Header Section (Optional)</text>
                        <text x="400" y="125" text-anchor="middle" fill="white" font-size="14">Lines starting with '@'</text>

                        <!-- Alignment section -->
                        <rect x="70" y="150" width="660" height="80" fill="url(#alignGrad)" rx="5"/>
                        <text x="400" y="185" text-anchor="middle" fill="white" font-size="18" font-weight="bold">Alignment Section</text>
                        <text x="400" y="205" text-anchor="middle" fill="white" font-size="14">11 mandatory fields + optional fields</text>
                        <text x="400" y="220" text-anchor="middle" fill="white" font-size="14">TAB-delimited lines</text>

                        <!-- Labels -->
                        <text x="400" y="30" text-anchor="middle" fill="#2c3e50" font-size="20" font-weight="bold">SAM File Structure</text>
                        <text x="400" y="270" text-anchor="middle" fill="#7f8c8d" font-size="12">Each line represents sequence alignment information</text>
                    </svg>
                </div>

                <h3>File Format Rules</h3>
                <div class="info">
                    <ul>
                        <li>Header lines start with <code>@</code></li>
                        <li>Alignment lines do not start with <code>@</code></li>
                        <li>Header must precede alignments (if present)</li>
                        <li>Each alignment line has 11 mandatory fields</li>
                        <li>Optional fields provide additional information</li>
                    </ul>
                </div>

                <h3>Coordinate Systems</h3>
                <div class="formula">
                    <p><strong>1-based coordinate system:</strong> First base = 1, regions specified as closed intervals [start, end]</p>
                    <p><strong>0-based coordinate system:</strong> First base = 0, regions specified as half-open intervals [start, end)</p>
                </div>

                <div class="warning">
                    <strong>Important:</strong> SAM uses 1-based coordinates, while BAM uses 0-based coordinates internally.
                </div>
            </section>

            <section id="structure" class="section">
                <h2>2. File Structure and Components</h2>

                <h3>Basic Example</h3>
                <div class="code-block">
@HD	VN:1.6	SO:coordinate
@SQ	SN:ref	LN:45
r001	99	ref	7	30	8M2I4M1D3M	=	37	39	TTAGATAAAGGATACTG	*
r002	0	ref	9	30	3S6M1P1I4M	*	0	0	AAAAGATAAGGATA	*
                </div>

                <div class="svg-container">
                    <svg width="900" height="400" viewBox="0 0 900 400">
                        <!-- Detailed SAM structure -->
                        <defs>
                            <pattern id="diagonalHatch" patternUnits="userSpaceOnUse" width="4" height="4">
                                <path d="M-1,1 l2,-2 M0,4 l4,-4 M3,5 l2,-2" style="stroke:#bdc3c7, stroke-width:1"/>
                            </pattern>
                        </defs>

                        <!-- Header section breakdown -->
                        <rect x="50" y="30" width="800" height="80" fill="#3498db" fill-opacity="0.2" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="60" y="50" fill="#2c3e50" font-size="16" font-weight="bold">Header Section</text>

                        <!-- @HD line -->
                        <rect x="70" y="60" width="200" height="25" fill="#3498db" fill-opacity="0.3" rx="3"/>
                        <text x="75" y="77" fill="#2c3e50" font-size="12">@HD VN:1.6 SO:coordinate</text>

                        <!-- @SQ line -->
                        <rect x="280" y="60" width="150" height="25" fill="#3498db" fill-opacity="0.3" rx="3"/>
                        <text x="285" y="77" fill="#2c3e50" font-size="12">@SQ SN:ref LN:45</text>

                        <!-- Alignment section breakdown -->
                        <rect x="50" y="130" width="800" height="220" fill="#e74c3c" fill-opacity="0.2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="60" y="150" fill="#2c3e50" font-size="16" font-weight="bold">Alignment Section</text>

                        <!-- Field labels -->
                        <text x="70" y="175" fill="#2c3e50" font-size="11" font-weight="bold">QNAME</text>
                        <text x="130" y="175" fill="#2c3e50" font-size="11" font-weight="bold">FLAG</text>
                        <text x="180" y="175" fill="#2c3e50" font-size="11" font-weight="bold">RNAME</text>
                        <text x="230" y="175" fill="#2c3e50" font-size="11" font-weight="bold">POS</text>
                        <text x="270" y="175" fill="#2c3e50" font-size="11" font-weight="bold">MAPQ</text>
                        <text x="320" y="175" fill="#2c3e50" font-size="11" font-weight="bold">CIGAR</text>
                        <text x="420" y="175" fill="#2c3e50" font-size="11" font-weight="bold">RNEXT</text>
                        <text x="470" y="175" fill="#2c3e50" font-size="11" font-weight="bold">PNEXT</text>
                        <text x="520" y="175" fill="#2c3e50" font-size="11" font-weight="bold">TLEN</text>
                        <text x="570" y="175" fill="#2c3e50" font-size="11" font-weight="bold">SEQ</text>
                        <text x="720" y="175" fill="#2c3e50" font-size="11" font-weight="bold">QUAL</text>

                        <!-- First alignment line -->
                        <rect x="70" y="185" width="50" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="75" y="198" fill="#2c3e50" font-size="10">r001</text>

                        <rect x="130" y="185" width="30" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="135" y="198" fill="#2c3e50" font-size="10">99</text>

                        <rect x="170" y="185" width="40" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="175" y="198" fill="#2c3e50" font-size="10">ref</text>

                        <rect x="220" y="185" width="30" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="225" y="198" fill="#2c3e50" font-size="10">7</text>

                        <rect x="260" y="185" width="30" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="265" y="198" fill="#2c3e50" font-size="10">30</text>

                        <rect x="300" y="185" width="100" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="305" y="198" fill="#2c3e50" font-size="10">8M2I4M1D3M</text>

                        <rect x="410" y="185" width="30" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="415" y="198" fill="#2c3e50" font-size="10">=</text>

                        <rect x="450" y="185" width="30" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="455" y="198" fill="#2c3e50" font-size="10">37</text>

                        <rect x="490" y="185" width="30" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="495" y="198" fill="#2c3e50" font-size="10">39</text>

                        <rect x="530" y="185" width="150" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="535" y="198" fill="#2c3e50" font-size="10">TTAGATAAAGGATACTG</text>

                        <rect x="690" y="185" width="30" height="20" fill="#f39c12" fill-opacity="0.3" rx="2"/>
                        <text x="695" y="198" fill="#2c3e50" font-size="10">*</text>

                        <!-- Arrows and descriptions -->
                        <line x1="95" y1="215" x2="95" y2="235" stroke="#34495e" stroke-width="1" marker-end="url(#arrowhead)"/>
                        <text x="60" y="250" fill="#34495e" font-size="10">Query name</text>

                        <line x1="145" y1="215" x2="145" y2="235" stroke="#34495e" stroke-width="1"/>
                        <text x="120" y="250" fill="#34495e" font-size="10">Bitwise flags</text>

                        <line x1="350" y1="215" x2="350" y2="235" stroke="#34495e" stroke-width="1"/>
                        <text x="300" y="250" fill="#34495e" font-size="10">CIGAR operations</text>

                        <line x1="605" y1="215" x2="605" y2="235" stroke="#34495e" stroke-width="1"/>
                        <text x="550" y="250" fill="#34495e" font-size="10">Read sequence</text>

                        <!-- Tab separators -->
                        <text x="120" y="200" fill="#e74c3c" font-size="8">TAB</text>
                        <text x="160" y="200" fill="#e74c3c" font-size="8">TAB</text>
                        <text x="210" y="200" fill="#e74c3c" font-size="8">TAB</text>
                        <text x="250" y="200" fill="#e74c3c" font-size="8">TAB</text>

                        <!-- Title -->
                        <text x="450" y="20" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">SAM File Structure Breakdown</text>

                        <!-- Legend -->
                        <rect x="50" y="370" width="15" height="15" fill="#3498db" fill-opacity="0.3"/>
                        <text x="75" y="382" fill="#2c3e50" font-size="12">Header Section</text>

                        <rect x="200" y="370" width="15" height="15" fill="#e74c3c" fill-opacity="0.3"/>
                        <text x="225" y="382" fill="#2c3e50" font-size="12">Alignment Section</text>

                        <rect x="350" y="370" width="15" height="15" fill="#f39c12" fill-opacity="0.3"/>
                        <text x="375" y="382" fill="#2c3e50" font-size="12">Individual Fields</text>
                    </svg>
                </div>

                <h3>Character Set and Encoding</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Component</th>
                                <th>Character Set</th>
                                <th>Restrictions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>General content</td>
                                <td>7-bit US-ASCII</td>
                                <td>UTF-8 for specific fields only</td>
                            </tr>
                            <tr>
                                <td>Query names</td>
                                <td>[!-~] except '@'</td>
                                <td>Printable ASCII, max 254 chars</td>
                            </tr>
                            <tr>
                                <td>Reference names</td>
                                <td>[:rname:]</td>
                                <td>No \,"'()[]{}⟨⟩, can't start with * or =</td>
                            </tr>
                            <tr>
                                <td>Line terminators</td>
                                <td>ASCII only</td>
                                <td>LF or CRLF, no BOM allowed</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section id="header" class="section">
                <h2>3. Header Section</h2>

                <div class="highlight">
                    Each header line begins with '@' followed by a two-letter record type code. Fields follow the format 'TAG:VALUE'.
                </div>

                <h3>Header Record Types</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Record Type</th>
                                <th>Description</th>
                                <th>Required</th>
                                <th>Multiple Allowed</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>@HD</code></td>
                                <td>File-level metadata</td>
                                <td>No</td>
                                <td>No (max 1, must be first)</td>
                            </tr>
                            <tr>
                                <td><code>@SQ</code></td>
                                <td>Reference sequence dictionary</td>
                                <td>Recommended</td>
                                <td>Yes</td>
                            </tr>
                            <tr>
                                <td><code>@RG</code></td>
                                <td>Read group information</td>
                                <td>No</td>
                                <td>Yes</td>
                            </tr>
                            <tr>
                                <td><code>@PG</code></td>
                                <td>Program information</td>
                                <td>No</td>
                                <td>Yes</td>
                            </tr>
                            <tr>
                                <td><code>@CO</code></td>
                                <td>One-line text comment</td>
                                <td>No</td>
                                <td>Yes</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>@HD - Header Line</h3>
                <div class="code-block">
@HD	VN:1.6	SO:coordinate	GO:none	SS:coordinate:queryname
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Tag</th>
                                <th>Description</th>
                                <th>Required</th>
                                <th>Valid Values</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>VN</code></td>
                                <td>Format version</td>
                                <td>Yes</td>
                                <td>Pattern: /^[0-9]+\.[0-9]+$/</td>
                            </tr>
                            <tr>
                                <td><code>SO</code></td>
                                <td>Sorting order</td>
                                <td>No</td>
                                <td>unknown, unsorted, queryname, coordinate</td>
                            </tr>
                            <tr>
                                <td><code>GO</code></td>
                                <td>Grouping of alignments</td>
                                <td>No</td>
                                <td>none, query, reference</td>
                            </tr>
                            <tr>
                                <td><code>SS</code></td>
                                <td>Sub-sorting order</td>
                                <td>No</td>
                                <td>Implementation-dependent</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>@SQ - Reference Sequence</h3>
                <div class="code-block">
@SQ	SN:chr1	LN:249250621	AS:GRCh38	SP:Homo sapiens	TP:linear
                </div>

                <div class="svg-container">
                    <svg width="800" height="250" viewBox="0 0 800 250">
                        <!-- Reference sequence visualization -->
                        <defs>
                            <linearGradient id="refGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Reference sequence representation -->
                        <rect x="100" y="80" width="600" height="40" fill="url(#refGrad)" rx="5"/>
                        <text x="400" y="105" text-anchor="middle" fill="white" font-size="16" font-weight="bold">Reference Sequence (SN:chr1)</text>

                        <!-- Length indicator -->
                        <line x1="100" y1="140" x2="700" y2="140" stroke="#34495e" stroke-width="2"/>
                        <line x1="100" y1="135" x2="100" y2="145" stroke="#34495e" stroke-width="2"/>
                        <line x1="700" y1="135" x2="700" y2="145" stroke="#34495e" stroke-width="2"/>
                        <text x="400" y="160" text-anchor="middle" fill="#34495e" font-size="14">Length: 249,250,621 bases (LN tag)</text>

                        <!-- Position markers -->
                        <text x="100" y="175" text-anchor="middle" fill="#34495e" font-size="12">Position 1</text>
                        <text x="700" y="175" text-anchor="middle" fill="#34495e" font-size="12">Position LN</text>

                        <!-- Coordinate system -->
                        <text x="400" y="200" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">1-based Coordinate System</text>
                        <text x="400" y="220" text-anchor="middle" fill="#7f8c8d" font-size="12">First base has coordinate 1</text>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Reference Sequence Structure</text>

                        <!-- Additional info boxes -->
                        <rect x="50" y="50" width="120" height="25" fill="#3498db" fill-opacity="0.2" rx="3"/>
                        <text x="110" y="67" text-anchor="middle" fill="#2c3e50" font-size="11">Assembly: GRCh38</text>

                        <rect x="630" y="50" width="120" height="25" fill="#e74c3c" fill-opacity="0.2" rx="3"/>
                        <text x="690" y="67" text-anchor="middle" fill="#2c3e50" font-size="11">Topology: Linear</text>
                    </svg>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Tag</th>
                                <th>Description</th>
                                <th>Required</th>
                                <th>Example</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>SN</code></td>
                                <td>Reference sequence name</td>
                                <td>Yes</td>
                                <td>chr1, MT, scaffold_1</td>
                            </tr>
                            <tr>
                                <td><code>LN</code></td>
                                <td>Reference sequence length</td>
                                <td>Yes</td>
                                <td>249250621</td>
                            </tr>
                            <tr>
                                <td><code>AS</code></td>
                                <td>Genome assembly identifier</td>
                                <td>No</td>
                                <td>GRCh38, hg19</td>
                            </tr>
                            <tr>
                                <td><code>SP</code></td>
                                <td>Species</td>
                                <td>No</td>
                                <td>Homo sapiens</td>
                            </tr>
                            <tr>
                                <td><code>TP</code></td>
                                <td>Molecule topology</td>
                                <td>No</td>
                                <td>linear (default), circular</td>
                            </tr>
                            <tr>
                                <td><code>M5</code></td>
                                <td>MD5 checksum</td>
                                <td>No</td>
                                <td>32-char hex string</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>MD5 Checksum Calculation</h3>
                <div class="formula">
                    <p>MD5 calculation steps:</p>
                    <ol>
                        <li>Strip all characters outside range 33-126 ('!' to '~')</li>
                        <li>Convert lowercase to uppercase</li>
                        <li>Calculate MD5 digest (RFC 1321)</li>
                        <li>Present as 32-character lowercase hexadecimal</li>
                    </ol>
                </div>

                <div class="code-block">
# Example reference sequence:
ACGT ACGT ACGT
acgt acgt acgt
... 12345 !!!

# After processing: ACGTACGTACGTACGTACGTACGT...12345!!!
# MD5: dfabdbb36e239a6da88957841f32b8e4
                </div>
            </section>

            <section id="alignment" class="section">
                <h2>4. Alignment Section - Mandatory Fields</h2>

                <div class="highlight">
                    Each alignment line represents the linear alignment of a segment with 11 mandatory TAB-separated fields.
                </div>

                <h3>The 11 Mandatory Fields</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Col</th>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Example</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td><code>QNAME</code></td>
                                <td>String</td>
                                <td>Query template name</td>
                                <td>r001, read_1</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td><code>FLAG</code></td>
                                <td>Int</td>
                                <td>Bitwise flags</td>
                                <td>99, 147, 0</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td><code>RNAME</code></td>
                                <td>String</td>
                                <td>Reference sequence name</td>
                                <td>chr1, *, ref</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td><code>POS</code></td>
                                <td>Int</td>
                                <td>1-based leftmost mapping position</td>
                                <td>7, 1000, 0</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td><code>MAPQ</code></td>
                                <td>Int</td>
                                <td>Mapping quality</td>
                                <td>30, 60, 255</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td><code>CIGAR</code></td>
                                <td>String</td>
                                <td>CIGAR string</td>
                                <td>8M2I4M1D3M, *</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td><code>RNEXT</code></td>
                                <td>String</td>
                                <td>Reference name of mate/next read</td>
                                <td>=, chr2, *</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td><code>PNEXT</code></td>
                                <td>Int</td>
                                <td>Position of mate/next read</td>
                                <td>37, 0</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td><code>TLEN</code></td>
                                <td>Int</td>
                                <td>Template length</td>
                                <td>39, -39, 0</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td><code>SEQ</code></td>
                                <td>String</td>
                                <td>Segment sequence</td>
                                <td>TTAGATAAAGGATACTG, *</td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td><code>QUAL</code></td>
                                <td>String</td>
                                <td>ASCII of Phred-scaled quality+33</td>
                                <td>IIIIIIIIIIIIIIIII, *</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>FLAG Field - Bitwise Flags</h3>
                <div class="svg-container">
                    <svg width="900" height="350" viewBox="0 0 900 350">
                        <!-- FLAG field visualization -->
                        <defs>
                            <linearGradient id="flagGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">FLAG Field Bit Representation</text>

                        <!-- Bit positions -->
                        <text x="50" y="60" fill="#2c3e50" font-size="14" font-weight="bold">Bit Position:</text>
                        <text x="50" y="85" fill="#2c3e50" font-size="14" font-weight="bold">Hex Value:</text>
                        <text x="50" y="110" fill="#2c3e50" font-size="14" font-weight="bold">Description:</text>

                        <!-- Bit boxes -->
                        <g id="bit-boxes">
                            <!-- Bit 1 (0x1) -->
                            <rect x="150" y="45" width="50" height="20" fill="url(#flagGrad)" rx="3"/>
                            <text x="175" y="58" text-anchor="middle" fill="white" font-size="12">1</text>
                            <text x="175" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x1</text>
                            <text x="175" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Multiple</text>
                            <text x="175" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">segments</text>

                            <!-- Bit 2 (0x2) -->
                            <rect x="210" y="45" width="50" height="20" fill="url(#flagGrad)" rx="3"/>
                            <text x="235" y="58" text-anchor="middle" fill="white" font-size="12">2</text>
                            <text x="235" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x2</text>
                            <text x="235" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Properly</text>
                            <text x="235" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">aligned</text>

                            <!-- Bit 4 (0x4) -->
                            <rect x="270" y="45" width="50" height="20" fill="#e74c3c" rx="3"/>
                            <text x="295" y="58" text-anchor="middle" fill="white" font-size="12">4</text>
                            <text x="295" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x4</text>
                            <text x="295" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Segment</text>
                            <text x="295" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">unmapped</text>

                            <!-- Bit 8 (0x8) -->
                            <rect x="330" y="45" width="50" height="20" fill="url(#flagGrad)" rx="3"/>
                            <text x="355" y="58" text-anchor="middle" fill="white" font-size="12">8</text>
                            <text x="355" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x8</text>
                            <text x="355" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Next segment</text>
                            <text x="355" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">unmapped</text>

                            <!-- Bit 16 (0x10) -->
                            <rect x="390" y="45" width="50" height="20" fill="#f39c12" rx="3"/>
                            <text x="415" y="58" text-anchor="middle" fill="white" font-size="12">16</text>
                            <text x="415" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x10</text>
                            <text x="415" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">SEQ reverse</text>
                            <text x="415" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">complemented</text>

                            <!-- Bit 32 (0x20) -->
                            <rect x="450" y="45" width="50" height="20" fill="url(#flagGrad)" rx="3"/>
                            <text x="475" y="58" text-anchor="middle" fill="white" font-size="12">32</text>
                            <text x="475" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x20</text>
                            <text x="475" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Next SEQ</text>
                            <text x="475" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">rev comp</text>

                            <!-- Bit 64 (0x40) -->
                            <rect x="510" y="45" width="50" height="20" fill="#27ae60" rx="3"/>
                            <text x="535" y="58" text-anchor="middle" fill="white" font-size="12">64</text>
                            <text x="535" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x40</text>
                            <text x="535" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">First segment</text>
                            <text x="535" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">in template</text>

                            <!-- Bit 128 (0x80) -->
                            <rect x="570" y="45" width="50" height="20" fill="#27ae60" rx="3"/>
                            <text x="595" y="58" text-anchor="middle" fill="white" font-size="12">128</text>
                            <text x="595" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x80</text>
                            <text x="595" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Last segment</text>
                            <text x="595" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">in template</text>

                            <!-- Bit 256 (0x100) -->
                            <rect x="630" y="45" width="50" height="20" fill="#e67e22" rx="3"/>
                            <text x="655" y="58" text-anchor="middle" fill="white" font-size="12">256</text>
                            <text x="655" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x100</text>
                            <text x="655" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Secondary</text>
                            <text x="655" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">alignment</text>

                            <!-- Bit 512 (0x200) -->
                            <rect x="690" y="45" width="50" height="20" fill="#e74c3c" rx="3"/>
                            <text x="715" y="58" text-anchor="middle" fill="white" font-size="12">512</text>
                            <text x="715" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x200</text>
                            <text x="715" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Not passing</text>
                            <text x="715" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">filters</text>

                            <!-- Bit 1024 (0x400) -->
                            <rect x="750" y="45" width="50" height="20" fill="#95a5a6" rx="3"/>
                            <text x="775" y="58" text-anchor="middle" fill="white" font-size="12">1024</text>
                            <text x="775" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x400</text>
                            <text x="775" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">PCR/optical</text>
                            <text x="775" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">duplicate</text>

                            <!-- Bit 2048 (0x800) -->
                            <rect x="810" y="45" width="50" height="20" fill="#e67e22" rx="3"/>
                            <text x="835" y="58" text-anchor="middle" fill="white" font-size="12">2048</text>
                            <text x="835" y="78" text-anchor="middle" fill="#2c3e50" font-size="10">0x800</text>
                            <text x="835" y="95" text-anchor="middle" fill="#2c3e50" font-size="8">Supplementary</text>
                            <text x="835" y="105" text-anchor="middle" fill="#2c3e50" font-size="8">alignment</text>
                        </g>

                        <!-- Example calculation -->
                        <text x="50" y="150" fill="#2c3e50" font-size="16" font-weight="bold">Example: FLAG = 99</text>
                        <text x="50" y="175" fill="#2c3e50" font-size="14">Binary: 01100011</text>
                        <text x="50" y="195" fill="#2c3e50" font-size="14">Bits set: 1 + 2 + 32 + 64 = 99</text>
                        <text x="50" y="215" fill="#2c3e50" font-size="14">Meaning: Multiple segments + Properly aligned + Next SEQ rev comp + First segment</text>

                        <!-- Legend -->
                        <text x="50" y="260" fill="#2c3e50" font-size="14" font-weight="bold">Color Legend:</text>
                        <rect x="50" y="275" width="15" height="15" fill="url(#flagGrad)"/>
                        <text x="75" y="287" fill="#2c3e50" font-size="12">General flags</text>

                        <rect x="200" y="275" width="15" height="15" fill="#e74c3c"/>
                        <text x="225" y="287" fill="#2c3e50" font-size="12">Quality/mapping issues</text>

                        <rect x="400" y="275" width="15" height="15" fill="#f39c12"/>
                        <text x="425" y="287" fill="#2c3e50" font-size="12">Strand orientation</text>

                        <rect x="600" y="275" width="15" height="15" fill="#27ae60"/>
                        <text x="625" y="287" fill="#2c3e50" font-size="12">Read pair position</text>

                        <rect x="50" y="300" width="15" height="15" fill="#e67e22"/>
                        <text x="75" y="312" fill="#2c3e50" font-size="12">Alternative alignments</text>

                        <rect x="250" y="300" width="15" height="15" fill="#95a5a6"/>
                        <text x="275" y="312" fill="#2c3e50" font-size="12">Duplicates</text>
                    </svg>
                </div>

                <div class="warning">
                    <strong>Important FLAG Rules:</strong>
                    <ul>
                        <li>One and only one line per read must satisfy 'FLAG & 0x900 == 0' (primary line)</li>
                        <li>Bit 0x4 is the only reliable indicator of unmapped reads</li>
                        <li>Bit 0x10 indicates if SEQ has been reverse complemented</li>
                    </ul>
                </div>
            </section>

            <section id="cigar" class="section">
                <h2>5. CIGAR Operations</h2>

                <div class="highlight">
                    CIGAR (Compact Idiosyncratic Gapped Alignment Report) describes how the read aligns to the reference, including matches, mismatches, insertions, deletions, and other operations.
                </div>

                <h3>CIGAR Operations Table</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Op</th>
                                <th>BAM</th>
                                <th>Description</th>
                                <th>Consumes Query</th>
                                <th>Consumes Reference</th>
                                <th>Example</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>M</code></td>
                                <td>0</td>
                                <td>Alignment match (match or mismatch)</td>
                                <td>Yes</td>
                                <td>Yes</td>
                                <td>10M</td>
                            </tr>
                            <tr>
                                <td><code>I</code></td>
                                <td>1</td>
                                <td>Insertion to the reference</td>
                                <td>Yes</td>
                                <td>No</td>
                                <td>2I</td>
                            </tr>
                            <tr>
                                <td><code>D</code></td>
                                <td>2</td>
                                <td>Deletion from the reference</td>
                                <td>No</td>
                                <td>Yes</td>
                                <td>1D</td>
                            </tr>
                            <tr>
                                <td><code>N</code></td>
                                <td>3</td>
                                <td>Skipped region from the reference</td>
                                <td>No</td>
                                <td>Yes</td>
                                <td>100N</td>
                            </tr>
                            <tr>
                                <td><code>S</code></td>
                                <td>4</td>
                                <td>Soft clipping (sequences present in SEQ)</td>
                                <td>Yes</td>
                                <td>No</td>
                                <td>5S</td>
                            </tr>
                            <tr>
                                <td><code>H</code></td>
                                <td>5</td>
                                <td>Hard clipping (sequences NOT present in SEQ)</td>
                                <td>No</td>
                                <td>No</td>
                                <td>3H</td>
                            </tr>
                            <tr>
                                <td><code>P</code></td>
                                <td>6</td>
                                <td>Padding (silent deletion from padded reference)</td>
                                <td>No</td>
                                <td>No</td>
                                <td>1P</td>
                            </tr>
                            <tr>
                                <td><code>=</code></td>
                                <td>7</td>
                                <td>Sequence match</td>
                                <td>Yes</td>
                                <td>Yes</td>
                                <td>8=</td>
                            </tr>
                            <tr>
                                <td><code>X</code></td>
                                <td>8</td>
                                <td>Sequence mismatch</td>
                                <td>Yes</td>
                                <td>Yes</td>
                                <td>2X</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>CIGAR Visualization Example</h3>
                <div class="code-block">
CIGAR: 8M2I4M1D3M
Read:  TTAGATAAAGGATACTG
Ref:   TTAGATAA--GATA-CTG
       ||||||||  ||||.|||
       12345678  9012.345
                </div>

                <div class="svg-container">
                    <svg width="900" height="400" viewBox="0 0 900 400">
                        <!-- CIGAR visualization -->
                        <defs>
                            <linearGradient id="matchGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="insertGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="deleteGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">CIGAR String: 8M2I4M1D3M</text>

                        <!-- Reference sequence -->
                        <text x="50" y="80" fill="#2c3e50" font-size="14" font-weight="bold">Reference:</text>
                        <rect x="150" y="65" width="600" height="25" fill="#ecf0f1" stroke="#bdc3c7" rx="3"/>

                        <!-- Query sequence -->
                        <text x="50" y="130" fill="#2c3e50" font-size="14" font-weight="bold">Query:</text>
                        <rect x="150" y="115" width="600" height="25" fill="#ecf0f1" stroke="#bdc3c7" rx="3"/>

                        <!-- CIGAR operations breakdown -->
                        <text x="50" y="180" fill="#2c3e50" font-size="14" font-weight="bold">Operations:</text>

                        <!-- 8M -->
                        <rect x="150" y="165" width="120" height="25" fill="url(#matchGrad)" rx="3"/>
                        <text x="210" y="182" text-anchor="middle" fill="white" font-size="12" font-weight="bold">8M</text>
                        <text x="210" y="205" text-anchor="middle" fill="#2c3e50" font-size="10">8 matches</text>

                        <!-- 2I -->
                        <rect x="280" y="165" width="60" height="25" fill="url(#insertGrad)" rx="3"/>
                        <text x="310" y="182" text-anchor="middle" fill="white" font-size="12" font-weight="bold">2I</text>
                        <text x="310" y="205" text-anchor="middle" fill="#2c3e50" font-size="10">2 insertions</text>

                        <!-- 4M -->
                        <rect x="350" y="165" width="80" height="25" fill="url(#matchGrad)" rx="3"/>
                        <text x="390" y="182" text-anchor="middle" fill="white" font-size="12" font-weight="bold">4M</text>
                        <text x="390" y="205" text-anchor="middle" fill="#2c3e50" font-size="10">4 matches</text>

                        <!-- 1D -->
                        <rect x="440" y="165" width="40" height="25" fill="url(#deleteGrad)" rx="3"/>
                        <text x="460" y="182" text-anchor="middle" fill="white" font-size="12" font-weight="bold">1D</text>
                        <text x="460" y="205" text-anchor="middle" fill="#2c3e50" font-size="10">1 deletion</text>

                        <!-- 3M -->
                        <rect x="490" y="165" width="60" height="25" fill="url(#matchGrad)" rx="3"/>
                        <text x="520" y="182" text-anchor="middle" fill="white" font-size="12" font-weight="bold">3M</text>
                        <text x="520" y="205" text-anchor="middle" fill="#2c3e50" font-size="10">3 matches</text>

                        <!-- Sequence alignment visualization -->
                        <!-- Reference bases -->
                        <text x="160" y="82" fill="#2c3e50" font-size="11" font-family="monospace">T T A G A T A A - - G A T A - C T G</text>

                        <!-- Query bases -->
                        <text x="160" y="132" fill="#2c3e50" font-size="11" font-family="monospace">T T A G A T A A A G G A T A C T G</text>

                        <!-- Alignment indicators -->
                        <text x="160" y="155" fill="#27ae60" font-size="11" font-family="monospace">| | | | | | | |</text>
                        <text x="280" y="155" fill="#3498db" font-size="11" font-family="monospace">+ +</text>
                        <text x="350" y="155" fill="#27ae60" font-size="11" font-family="monospace">| | | |</text>
                        <text x="440" y="155" fill="#e74c3c" font-size="11" font-family="monospace">-</text>
                        <text x="490" y="155" fill="#27ae60" font-size="11" font-family="monospace">| | |</text>

                        <!-- Position numbers -->
                        <text x="50" y="250" fill="#2c3e50" font-size="14" font-weight="bold">Positions:</text>
                        <text x="160" y="250" fill="#7f8c8d" font-size="10" font-family="monospace">1 2 3 4 5 6 7 8     9 0 1 2   3 4 5</text>

                        <!-- Legend -->
                        <text x="50" y="300" fill="#2c3e50" font-size="14" font-weight="bold">Legend:</text>
                        <rect x="50" y="315" width="15" height="15" fill="url(#matchGrad)"/>
                        <text x="75" y="327" fill="#2c3e50" font-size="12">Match/Mismatch (M)</text>

                        <rect x="250" y="315" width="15" height="15" fill="url(#insertGrad)"/>
                        <text x="275" y="327" fill="#2c3e50" font-size="12">Insertion (I)</text>

                        <rect x="400" y="315" width="15" height="15" fill="url(#deleteGrad)"/>
                        <text x="425" y="327" fill="#2c3e50" font-size="12">Deletion (D)</text>

                        <!-- Rules -->
                        <text x="50" y="360" fill="#2c3e50" font-size="12">Rules: Sum of M/I/S/=/X operations = length of SEQ</text>
                        <text x="50" y="380" fill="#2c3e50" font-size="12">H can only be first and/or last operation</text>
                    </svg>
                </div>

                <h3>CIGAR Rules and Constraints</h3>
                <div class="info">
                    <ul>
                        <li><strong>Length constraint:</strong> Sum of M/I/S/=/X operations must equal SEQ length</li>
                        <li><strong>Hard clipping:</strong> H can only be first and/or last operation</li>
                        <li><strong>Soft clipping:</strong> S may only have H operations between them and CIGAR ends</li>
                        <li><strong>Introns:</strong> N operation represents introns in mRNA-to-genome alignment</li>
                        <li><strong>Adjacent operations:</strong> Should be different (best practice)</li>
                    </ul>
                </div>

                <h3>Quality Scores</h3>
                <div class="formula">
                    <p><strong>Mapping Quality (MAPQ):</strong></p>
                    $$MAPQ = -10 \log_{10} P(\text{mapping position is wrong})$$

                    <p><strong>Base Quality (QUAL):</strong></p>
                    $$Q = -10 \log_{10} P(\text{base is wrong})$$
                    <p>Encoded as ASCII character = Q + 33 (Phred+33 encoding)</p>
                </div>

                <div class="code-block">
# Quality score examples:
# Q=20 → P(error)=0.01 → ASCII='5' (53)
# Q=30 → P(error)=0.001 → ASCII='?' (63)
# Q=40 → P(error)=0.0001 → ASCII='I' (73)
                </div>
            </section>

            <section id="examples" class="section">
                <h2>6. Complete Examples</h2>

                <h3>Example 1: Simple Alignment</h3>
                <div class="code-block">
@HD	VN:1.6	SO:coordinate
@SQ	SN:ref	LN:45
@RG	ID:group1	SM:sample1	PL:ILLUMINA
r001	99	ref	7	30	8M2I4M1D3M	=	37	39	TTAGATAAAGGATACTG	*
r001	147	ref	37	30	9M	=	7	-39	CAGCGGCAT	*	NM:i:1
                </div>

                <div class="svg-container">
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Paired-end alignment visualization -->
                        <defs>
                            <linearGradient id="read1Grad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="read2Grad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Reference sequence -->
                        <rect x="50" y="100" width="700" height="30" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="5"/>
                        <text x="400" y="120" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Reference Sequence (ref)</text>

                        <!-- Position markers -->
                        <text x="50" y="95" fill="#7f8c8d" font-size="10">1</text>
                        <text x="150" y="95" fill="#7f8c8d" font-size="10">7</text>
                        <text x="550" y="95" fill="#7f8c8d" font-size="10">37</text>
                        <text x="750" y="95" fill="#7f8c8d" font-size="10">45</text>

                        <!-- Read 1 (r001, FLAG=99) -->
                        <rect x="150" y="150" width="200" height="25" fill="url(#read1Grad)" rx="3"/>
                        <text x="250" y="167" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Read 1 (r001)</text>
                        <text x="250" y="185" text-anchor="middle" fill="#2c3e50" font-size="10">POS=7, FLAG=99, TLEN=39</text>

                        <!-- Read 2 (r001, FLAG=147) -->
                        <rect x="550" y="200" width="150" height="25" fill="url(#read2Grad)" rx="3"/>
                        <text x="625" y="217" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Read 2 (r001)</text>
                        <text x="625" y="235" text-anchor="middle" fill="#2c3e50" font-size="10">POS=37, FLAG=147, TLEN=-39</text>

                        <!-- Template span -->
                        <line x1="150" y1="250" x2="700" y2="250" stroke="#f39c12" stroke-width="3"/>
                        <line x1="150" y1="245" x2="150" y2="255" stroke="#f39c12" stroke-width="3"/>
                        <line x1="700" y1="245" x2="700" y2="255" stroke="#f39c12" stroke-width="3"/>
                        <text x="425" y="270" text-anchor="middle" fill="#f39c12" font-size="12" font-weight="bold">Template Length = 39 bp</text>

                        <!-- Arrows showing mate relationships -->
                        <path d="M 350 162 Q 450 140 550 212" stroke="#9b59b6" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        <path d="M 550 212 Q 450 190 350 162" stroke="#9b59b6" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Paired-End Read Alignment</text>

                        <!-- Legend -->
                        <rect x="50" y="50" width="15" height="15" fill="url(#read1Grad)"/>
                        <text x="75" y="62" fill="#2c3e50" font-size="12">First in pair (0x40)</text>

                        <rect x="250" y="50" width="15" height="15" fill="url(#read2Grad)"/>
                        <text x="275" y="62" fill="#2c3e50" font-size="12">Last in pair (0x80)</text>

                        <line x1="450" y1="57" x2="480" y2="57" stroke="#9b59b6" stroke-width="2"/>
                        <text x="490" y="62" fill="#2c3e50" font-size="12">Mate relationship</text>
                    </svg>
                </div>

                <h3>Example 2: Chimeric Alignment</h3>
                <div class="code-block">
@HD	VN:1.6	SO:coordinate
@SQ	SN:ref	LN:45
r003	0	ref	9	30	5S6M	*	0	0	GCCTAAGCTAA	*	SA:Z:ref,29,-,6H5M,17,0;
r003	2064	ref	29	17	6H5M	*	0	0	TAGGC	*	SA:Z:ref,9,+,5S6M,30,1;
                </div>

                <div class="info">
                    <strong>Chimeric Alignment Explanation:</strong>
                    <ul>
                        <li>Read r003 has two alignment segments</li>
                        <li>First segment: FLAG=0 (primary), POS=9, CIGAR=5S6M</li>
                        <li>Second segment: FLAG=2064 (supplementary + reverse complement), POS=29, CIGAR=6H5M</li>
                        <li>SA tag links the segments together</li>
                    </ul>
                </div>

                <h3>Example 3: Optional Fields</h3>
                <div class="code-block">
r001	99	ref	7	30	8M2I4M1D3M	=	37	39	TTAGATAAAGGATACTG	*	NM:i:1	MD:Z:8^A7	AS:i:18	XS:i:12
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Tag</th>
                                <th>Type</th>
                                <th>Value</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>NM</code></td>
                                <td>i</td>
                                <td>1</td>
                                <td>Edit distance to reference</td>
                            </tr>
                            <tr>
                                <td><code>MD</code></td>
                                <td>Z</td>
                                <td>8^A7</td>
                                <td>String for mismatching positions</td>
                            </tr>
                            <tr>
                                <td><code>AS</code></td>
                                <td>i</td>
                                <td>18</td>
                                <td>Alignment score</td>
                            </tr>
                            <tr>
                                <td><code>XS</code></td>
                                <td>i</td>
                                <td>12</td>
                                <td>Suboptimal alignment score</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section id="best-practices" class="section">
                <h2>7. Best Practices and Recommendations</h2>

                <h3>Header Section Best Practices</h3>
                <div class="highlight">
                    <ul>
                        <li>Always include @HD line with VN and SO/GO tags</li>
                        <li>Include @SQ lines if reads have been mapped</li>
                        <li>Ensure @RG and @PG lines match tags in alignment section</li>
                        <li>Use consistent reference sequence names</li>
                    </ul>
                </div>

                <h3>Alignment Section Best Practices</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Aspect</th>
                                <th>Recommendation</th>
                                <th>Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CIGAR operations</td>
                                <td>Adjacent operations should be different</td>
                                <td>Efficiency and clarity</td>
                            </tr>
                            <tr>
                                <td>Mapping quality</td>
                                <td>No alignments should have MAPQ=255</td>
                                <td>255 indicates unavailable quality</td>
                            </tr>
                            <tr>
                                <td>Unmapped reads</td>
                                <td>Store in original orientation</td>
                                <td>Preserve sequencing machine output</td>
                            </tr>
                            <tr>
                                <td>Secondary alignments</td>
                                <td>Set SEQ and QUAL to '*'</td>
                                <td>Reduce file size</td>
                            </tr>
                            <tr>
                                <td>Optional tags</td>
                                <td>Include NM and TC tags when applicable</td>
                                <td>Standard compatibility</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Common Pitfalls to Avoid</h3>
                <div class="warning">
                    <ul>
                        <li><strong>Coordinate confusion:</strong> Remember SAM uses 1-based coordinates</li>
                        <li><strong>FLAG misinterpretation:</strong> Use bit 0x4 to check if read is unmapped</li>
                        <li><strong>CIGAR validation:</strong> Ensure M/I/S/=/X operations sum equals SEQ length</li>
                        <li><strong>Quality encoding:</strong> Use Phred+33 encoding for QUAL field</li>
                        <li><strong>Reference consistency:</strong> RNAME must match @SQ SN tags</li>
                    </ul>
                </div>

                <h3>File Validation Checklist</h3>
                <div class="info">
                    <ol>
                        <li>Header section precedes alignment section</li>
                        <li>All RNAME values present in @SQ lines (if not '*')</li>
                        <li>FLAG values are valid combinations</li>
                        <li>CIGAR strings follow operation rules</li>
                        <li>Quality strings match sequence lengths</li>
                        <li>Optional field formats are correct</li>
                        <li>File uses proper line terminators</li>
                    </ol>
                </div>

                <div class="svg-container">
                    <svg width="800" height="200" viewBox="0 0 800 200">
                        <!-- Workflow diagram -->
                        <defs>
                            <linearGradient id="workflowGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#0984e3;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">SAM File Processing Workflow</text>

                        <!-- Workflow steps -->
                        <rect x="50" y="50" width="120" height="40" fill="url(#workflowGrad)" rx="5"/>
                        <text x="110" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Raw Reads</text>

                        <path d="M 170 70 L 200 70" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>

                        <rect x="210" y="50" width="120" height="40" fill="url(#workflowGrad)" rx="5"/>
                        <text x="270" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Alignment</text>

                        <path d="M 330 70 L 360 70" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>

                        <rect x="370" y="50" width="120" height="40" fill="url(#workflowGrad)" rx="5"/>
                        <text x="430" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">SAM Output</text>

                        <path d="M 490 70 L 520 70" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>

                        <rect x="530" y="50" width="120" height="40" fill="url(#workflowGrad)" rx="5"/>
                        <text x="590" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Analysis</text>

                        <!-- Process descriptions -->
                        <text x="110" y="110" text-anchor="middle" fill="#7f8c8d" font-size="10">FASTQ files</text>
                        <text x="270" y="110" text-anchor="middle" fill="#7f8c8d" font-size="10">BWA, Bowtie2</text>
                        <text x="430" y="110" text-anchor="middle" fill="#7f8c8d" font-size="10">Validate format</text>
                        <text x="590" y="110" text-anchor="middle" fill="#7f8c8d" font-size="10">Variant calling</text>

                        <!-- Best practices notes -->
                        <text x="400" y="150" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Key Validation Points</text>
                        <text x="400" y="170" text-anchor="middle" fill="#7f8c8d" font-size="12">Header consistency • CIGAR validation • FLAG verification • Quality encoding</text>
                    </svg>
                </div>
            </section>
        </div>
    </div>
</body>
</html>
    </div>
</body>
</html>
