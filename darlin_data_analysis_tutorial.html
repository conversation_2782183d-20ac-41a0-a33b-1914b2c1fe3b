<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DARLIN Data Analysis Strategies - Comprehensive Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #9b59b6;
            --light-bg: #ecf0f1;
            --dark-bg: #34495e;
            --text-color: #2c3e50;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
        }

        .outline {
            background: white;
            padding: 35px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            margin-bottom: 30px;
            border-left: 6px solid var(--info-color);
        }

        .outline h2 {
            color: var(--primary-color);
            margin-bottom: 25px;
            font-size: 2em;
        }

        .outline ol {
            padding-left: 25px;
        }

        .outline li {
            margin-bottom: 12px;
            font-size: 1.1em;
        }

        .outline li ul {
            margin-top: 8px;
            padding-left: 25px;
        }

        .outline li ul li {
            font-size: 1em;
            color: #666;
            margin-bottom: 6px;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-left: 6px solid var(--accent-color);
        }

        .section-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
            padding: 25px 35px;
            font-size: 1.6em;
            font-weight: bold;
        }

        .section-content {
            padding: 35px;
        }

        .method-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid var(--success-color);
            padding: 25px;
            margin: 25px 0;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .method-number {
            background: var(--success-color);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            font-size: 1.1em;
        }

        .formula-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid var(--warning-color);
            padding: 25px;
            margin: 25px 0;
            border-radius: 12px;
            border-left: 6px solid var(--warning-color);
        }

        .analysis-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .analysis-table th {
            background: var(--primary-color);
            color: white;
            padding: 18px;
            text-align: left;
            font-size: 1.1em;
        }

        .analysis-table td {
            padding: 15px 18px;
            border-bottom: 1px solid var(--border-color);
        }

        .analysis-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .svg-container {
            text-align: center;
            margin: 35px 0;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .key-insight {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left: 5px solid var(--success-color);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .workflow-step {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 5px solid var(--secondary-color);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            max-width: 220px;
            z-index: 1000;
        }

        .navigation h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .navigation ul {
            list-style: none;
        }

        .navigation li {
            margin-bottom: 8px;
        }

        .navigation a {
            color: var(--secondary-color);
            text-decoration: none;
            font-size: 0.95em;
            display: block;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.3s;
        }

        .navigation a:hover {
            background: var(--light-bg);
            transform: translateX(5px);
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2.2em;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <h3>Quick Navigation</h3>
        <ul>
            <li><a href="#overview">Analysis Overview</a></li>
            <li><a href="#quality-control">Quality Control</a></li>
            <li><a href="#diversity-metrics">Diversity Metrics</a></li>
            <li><a href="#clonal-analysis">Clonal Analysis</a></li>
            <li><a href="#computational">Computational Methods</a></li>
            <li><a href="#multi-omics">Multi-omics Integration</a></li>
            <li><a href="#statistical">Statistical Approaches</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>DARLIN Data Analysis Strategies</h1>
            <p>Comprehensive Guide to Single-Cell Lineage Tracing Analysis</p>
        </div>

        <div class="outline">
            <h2>📊 Analysis Strategy Outline</h2>
            <ol>
                <li><strong>Data Analysis Overview</strong>
                    <ul>
                        <li>Multi-modal data integration approach</li>
                        <li>Quality control pipeline design</li>
                        <li>Statistical framework overview</li>
                    </ul>
                </li>
                <li><strong>Quality Control and Filtering</strong>
                    <ul>
                        <li>Three-step QC pipeline</li>
                        <li>Rare allele identification</li>
                        <li>Homoplasy probability assessment</li>
                    </ul>
                </li>
                <li><strong>Diversity and Complexity Metrics</strong>
                    <ul>
                        <li>Shannon entropy calculations</li>
                        <li>Singleton fraction analysis</li>
                        <li>Good-Turing estimator application</li>
                    </ul>
                </li>
                <li><strong>Clonal Analysis Methods</strong>
                    <ul>
                        <li>Clone ID assignment strategy</li>
                        <li>Clonal coupling score computation</li>
                        <li>Shared clone fraction analysis</li>
                    </ul>
                </li>
                <li><strong>Computational Approaches</strong>
                    <ul>
                        <li>CoSpar fate prediction algorithm</li>
                        <li>UMAP dimensionality reduction</li>
                        <li>Pseudotime trajectory inference</li>
                    </ul>
                </li>
                <li><strong>Multi-omics Integration</strong>
                    <ul>
                        <li>Camellia-seq data processing</li>
                        <li>Cross-modal correlation analysis</li>
                        <li>Integrated visualization strategies</li>
                    </ul>
                </li>
                <li><strong>Statistical Testing and Validation</strong>
                    <ul>
                        <li>Robustness testing approaches</li>
                        <li>Down-sampling validation</li>
                        <li>Cross-dataset comparisons</li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 1: Data Analysis Overview -->
        <div class="section" id="overview">
            <div class="section-header">
                📈 1. Data Analysis Overview
            </div>
            <div class="section-content">
                <h3>Multi-Modal Data Integration Strategy</h3>
                <p>DARLIN data analysis involves integrating multiple types of molecular information to reconstruct cellular lineage relationships and understand cell fate decisions.</p>

                <div class="svg-container">
                    <svg width="800" height="450" viewBox="0 0 800 450">
                        <rect width="800" height="450" fill="#f8f9fa" rx="10"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">DARLIN Data Analysis Framework</text>

                        <!-- Data Types -->
                        <rect x="50" y="50" width="150" height="80" fill="#e74c3c" rx="8"/>
                        <text x="125" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Lineage Barcodes</text>
                        <text x="125" y="90" text-anchor="middle" font-size="10" fill="white">• CA, TA, RA arrays</text>
                        <text x="125" y="105" text-anchor="middle" font-size="10" fill="white">• Editing patterns</text>
                        <text x="125" y="120" text-anchor="middle" font-size="10" fill="white">• Clone identities</text>

                        <rect x="225" y="50" width="150" height="80" fill="#27ae60" rx="8"/>
                        <text x="300" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Transcriptomics</text>
                        <text x="300" y="90" text-anchor="middle" font-size="10" fill="white">• Gene expression</text>
                        <text x="300" y="105" text-anchor="middle" font-size="10" fill="white">• Cell type identity</text>
                        <text x="300" y="120" text-anchor="middle" font-size="10" fill="white">• Functional states</text>

                        <rect x="400" y="50" width="150" height="80" fill="#3498db" rx="8"/>
                        <text x="475" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Epigenomics</text>
                        <text x="475" y="90" text-anchor="middle" font-size="10" fill="white">• DNA methylation</text>
                        <text x="475" y="105" text-anchor="middle" font-size="10" fill="white">• Chromatin access.</text>
                        <text x="475" y="120" text-anchor="middle" font-size="10" fill="white">• Regulatory states</text>

                        <rect x="575" y="50" width="150" height="80" fill="#9b59b6" rx="8"/>
                        <text x="650" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Spatial Info</text>
                        <text x="650" y="90" text-anchor="middle" font-size="10" fill="white">• Tissue location</text>
                        <text x="650" y="105" text-anchor="middle" font-size="10" fill="white">• Migration patterns</text>
                        <text x="650" y="120" text-anchor="middle" font-size="10" fill="white">• Niche interactions</text>

                        <!-- Integration arrows -->
                        <path d="M 125 140 L 400 180" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 300 140 L 400 180" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 475 140 L 400 180" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 650 140 L 400 180" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>

                        <!-- Integration hub -->
                        <circle cx="400" cy="190" r="25" fill="#f39c12"/>
                        <text x="400" y="197" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Integration</text>

                        <!-- Analysis outputs -->
                        <rect x="150" y="240" width="120" height="60" fill="#e8f5e8" stroke="#27ae60" rx="8"/>
                        <text x="210" y="260" text-anchor="middle" font-size="11" font-weight="bold" fill="#27ae60">Lineage Trees</text>
                        <text x="210" y="275" text-anchor="middle" font-size="9" fill="#333">Clonal relationships</text>
                        <text x="210" y="290" text-anchor="middle" font-size="9" fill="#333">Fate mapping</text>

                        <rect x="290" y="240" width="120" height="60" fill="#e3f2fd" stroke="#3498db" rx="8"/>
                        <text x="350" y="260" text-anchor="middle" font-size="11" font-weight="bold" fill="#3498db">Cell States</text>
                        <text x="350" y="275" text-anchor="middle" font-size="9" fill="#333">Transcriptomic</text>
                        <text x="350" y="290" text-anchor="middle" font-size="9" fill="#333">signatures</text>

                        <rect x="430" y="240" width="120" height="60" fill="#f3e5f5" stroke="#9b59b6" rx="8"/>
                        <text x="490" y="260" text-anchor="middle" font-size="11" font-weight="bold" fill="#9b59b6">Migration</text>
                        <text x="490" y="275" text-anchor="middle" font-size="9" fill="#333">Dynamics</text>
                        <text x="490" y="290" text-anchor="middle" font-size="9" fill="#333">Patterns</text>

                        <!-- Output arrows -->
                        <path d="M 400 215 L 210 240" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 400 215 L 350 240" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 400 215 L 490 240" stroke="#9b59b6" stroke-width="2" marker-end="url(#arrowhead)"/>

                        <!-- Analysis pipeline -->
                        <rect x="50" y="330" width="700" height="100" fill="white" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="400" y="350" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Core Analysis Pipeline</text>

                        <circle cx="120" cy="380" r="8" fill="#e74c3c"/>
                        <text x="140" y="385" font-size="11" fill="#333">1. Raw Data Processing</text>

                        <circle cx="280" cy="380" r="8" fill="#f39c12"/>
                        <text x="300" y="385" font-size="11" fill="#333">2. Quality Control</text>

                        <circle cx="420" cy="380" r="8" fill="#3498db"/>
                        <text x="440" y="385" font-size="11" fill="#333">3. Barcode Assignment</text>

                        <circle cx="580" cy="380" r="8" fill="#27ae60"/>
                        <text x="600" y="385" font-size="11" fill="#333">4. Lineage Reconstruction</text>

                        <circle cx="120" cy="405" r="8" fill="#9b59b6"/>
                        <text x="140" y="410" font-size="11" fill="#333">5. Statistical Analysis</text>

                        <circle cx="320" cy="405" r="8" fill="#34495e"/>
                        <text x="340" y="410" font-size="11" fill="#333">6. Biological Interpretation</text>

                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Key Analysis Principles</h3>
                <div class="workflow-step">
                    <strong>🎯 Principle 1: Multi-level Integration</strong>
                    <p>Combine information from three independent target arrays (CA, TA, RA) to create robust clone identities and reduce false positives.</p>
                </div>

                <div class="workflow-step">
                    <strong>🎯 Principle 2: Stringent Quality Control</strong>
                    <p>Apply rigorous filtering to ensure only high-quality, informative barcodes are used for downstream analysis.</p>
                </div>

                <div class="workflow-step">
                    <strong>🎯 Principle 3: Statistical Robustness</strong>
                    <p>Validate findings through multiple statistical approaches, down-sampling, and cross-validation methods.</p>
                </div>

                <div class="key-insight">
                    <strong>💡 Core Insight:</strong> The analysis strategy prioritizes <em>rare alleles</em> over common ones, as rare alleles provide more reliable lineage information and reduce the risk of barcode homoplasy (identical barcodes arising independently).
                </div>
            </div>
        </div>

        <!-- Section 2: Quality Control -->
        <div class="section" id="quality-control">
            <div class="section-header">
                🔍 2. Quality Control and Filtering Pipeline
            </div>
            <div class="section-content">
                <h3>Three-Step Quality Control Pipeline</h3>
                <p>DARLIN employs a rigorous three-step filtering process to ensure high-quality lineage data:</p>

                <div class="svg-container">
                    <svg width="750" height="400" viewBox="0 0 750 400">
                        <rect width="750" height="400" fill="#f8f9fa" rx="10"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">DARLIN Quality Control Pipeline</text>

                        <!-- Input cells -->
                        <rect x="50" y="60" width="100" height="50" fill="#95a5a6" rx="8"/>
                        <text x="100" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">All Cells</text>
                        <text x="100" y="95" text-anchor="middle" font-size="10" fill="white">6,094 cells</text>

                        <!-- Step 1: Detection -->
                        <rect x="200" y="60" width="100" height="50" fill="#3498db" rx="8"/>
                        <text x="250" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Step 1</text>
                        <text x="250" y="88" text-anchor="middle" font-size="10" fill="white">Detected</text>
                        <text x="250" y="101" text-anchor="middle" font-size="10" fill="white">81% pass</text>

                        <!-- Step 2: Edited -->
                        <rect x="350" y="60" width="100" height="50" fill="#f39c12" rx="8"/>
                        <text x="400" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Step 2</text>
                        <text x="400" y="88" text-anchor="middle" font-size="10" fill="white">Edited</text>
                        <text x="400" y="101" text-anchor="middle" font-size="10" fill="white">~80% pass</text>

                        <!-- Step 3: Rare -->
                        <rect x="500" y="60" width="100" height="50" fill="#27ae60" rx="8"/>
                        <text x="550" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Step 3</text>
                        <text x="550" y="88" text-anchor="middle" font-size="10" fill="white">Rare</text>
                        <text x="550" y="101" text-anchor="middle" font-size="10" fill="white">~93% pass</text>

                        <!-- Final output -->
                        <rect x="650" y="60" width="100" height="50" fill="#e74c3c" rx="8"/>
                        <text x="700" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Final</text>
                        <text x="700" y="88" text-anchor="middle" font-size="10" fill="white">Usable</text>
                        <text x="700" y="101" text-anchor="middle" font-size="10" fill="white">~60% total</text>

                        <!-- Arrows -->
                        <path d="M 150 85 L 190 85" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>
                        <path d="M 300 85 L 340 85" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>
                        <path d="M 450 85 L 490 85" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>
                        <path d="M 600 85 L 640 85" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>

                        <!-- Detailed explanations -->
                        <rect x="50" y="140" width="650" height="220" fill="white" stroke="#bdc3c7" rx="10"/>
                        <text x="375" y="165" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Detailed QC Criteria</text>

                        <!-- Step 1 details -->
                        <rect x="70" y="180" width="140" height="80" fill="#e3f2fd" stroke="#3498db" rx="5"/>
                        <text x="140" y="200" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Detection Filter</text>
                        <text x="140" y="215" text-anchor="middle" font-size="9" fill="#333">• Barcode expression detected</text>
                        <text x="140" y="228" text-anchor="middle" font-size="9" fill="#333">• Sufficient read coverage</text>
                        <text x="140" y="241" text-anchor="middle" font-size="9" fill="#333">• At least 1 of 3 arrays</text>
                        <text x="140" y="254" text-anchor="middle" font-size="9" fill="#333">• RNA quality passed</text>

                        <!-- Step 2 details -->
                        <rect x="230" y="180" width="140" height="80" fill="#fff3cd" stroke="#f39c12" rx="5"/>
                        <text x="300" y="200" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Editing Filter</text>
                        <text x="300" y="215" text-anchor="middle" font-size="9" fill="#333">• Barcode shows editing</text>
                        <text x="300" y="228" text-anchor="middle" font-size="9" fill="#333">• Differs from reference</text>
                        <text x="300" y="241" text-anchor="middle" font-size="9" fill="#333">• Insertions/deletions</text>
                        <text x="300" y="254" text-anchor="middle" font-size="9" fill="#333">• Complexity threshold</text>

                        <!-- Step 3 details -->
                        <rect x="390" y="180" width="140" height="80" fill="#e8f5e8" stroke="#27ae60" rx="5"/>
                        <text x="460" y="200" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Rarity Filter</text>
                        <text x="460" y="215" text-anchor="middle" font-size="9" fill="#333">• Low homoplasy risk</text>
                        <text x="460" y="228" text-anchor="middle" font-size="9" fill="#333">• P(homoplasy) < 0.05</text>
                        <text x="460" y="241" text-anchor="middle" font-size="9" fill="#333">• Unique in allele bank</text>
                        <text x="460" y="254" text-anchor="middle" font-size="9" fill="#333">• Clone-informative</text>

                        <!-- Final criteria -->
                        <rect x="550" y="180" width="140" height="80" fill="#ffebee" stroke="#e74c3c" rx="5"/>
                        <text x="620" y="200" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Final Criteria</text>
                        <text x="620" y="215" text-anchor="middle" font-size="9" fill="#333">• All 3 filters passed</text>
                        <text x="620" y="228" text-anchor="middle" font-size="9" fill="#333">• High confidence</text>
                        <text x="620" y="241" text-anchor="middle" font-size="9" fill="#333">• Lineage informative</text>
                        <text x="620" y="254" text-anchor="middle" font-size="9" fill="#333">• Ready for analysis</text>

                        <!-- Performance comparison -->
                        <text x="375" y="290" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Performance vs Cas9/CARLIN</text>

                        <!-- DARLIN bar -->
                        <rect x="200" y="305" width="240" height="20" fill="#27ae60" rx="3"/>
                        <text x="320" y="318" text-anchor="middle" font-size="11" fill="white">DARLIN: 60%</text>

                        <!-- CARLIN bar -->
                        <rect x="200" y="330" width="40" height="20" fill="#e74c3c" rx="3"/>
                        <text x="220" y="343" text-anchor="middle" font-size="11" fill="white">10%</text>
                        <text x="280" y="343" text-anchor="middle" font-size="11" fill="#333">Cas9/CARLIN</text>

                        <defs>
                            <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Homoplasy Probability Assessment</h3>
                <p>A critical component of the QC pipeline is assessing the probability that identical barcodes arose independently:</p>

                <div class="formula-box">
                    <h4>Homoplasy Probability Calculation</h4>
                    <p>For an allele observed $n$ times in a dataset of $N$ total observations:</p>
                    $$P_{homoplasy} = 1 - \left(\frac{f_{intrinsic}}{f_{observed}}\right)^n$$
                    <p>Where:</p>
                    <ul>
                        <li>$f_{intrinsic}$ = intrinsic frequency from allele bank (~10⁵ alleles)</li>
                        <li>$f_{observed}$ = observed frequency in current dataset</li>
                        <li>$n$ = number of times allele is observed</li>
                    </ul>
                    <p><strong>Threshold:</strong> $P_{homoplasy} < 0.05$ for rare allele classification</p>
                </div>

                <div class="method-box">
                    <span class="method-number">1</span>
                    <strong>Allele Bank Construction:</strong> Build a comprehensive database of ~100,000 alleles from multiple bulk DARLIN experiments to establish intrinsic allele frequencies and identify common vs. rare editing patterns.
                </div>
            </div>
        </div>

        <!-- Section 3: Diversity Metrics -->
        <div class="section" id="diversity-metrics">
            <div class="section-header">
                📊 3. Diversity and Complexity Metrics
            </div>
            <div class="section-content">
                <h3>Shannon Entropy for Barcode Diversity</h3>
                <p>The primary metric for quantifying barcode diversity in DARLIN is Shannon entropy, which accounts for both the number of unique alleles and their frequency distribution.</p>

                <div class="formula-box">
                    <h4>Shannon Diversity Index</h4>
                    $$H = -\sum_{i=1}^{S} p_i \log_2 p_i$$
                    $$\text{Shannon Diversity} = 2^H$$
                    <p>Where:</p>
                    <ul>
                        <li>$S$ = total number of unique alleles</li>
                        <li>$p_i$ = frequency of allele $i$ (normalized)</li>
                        <li>$H$ = Shannon entropy</li>
                    </ul>
                    <p><strong>Interpretation:</strong> Higher values indicate greater barcode diversity</p>
                </div>

                <div class="svg-container">
                    <svg width="700" height="350" viewBox="0 0 700 350">
                        <rect width="700" height="350" fill="#f8f9fa" rx="10"/>

                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Shannon Diversity Comparison</text>

                        <!-- DARLIN diversity -->
                        <rect x="100" y="50" width="200" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="8"/>
                        <text x="200" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">DARLIN</text>
                        <text x="200" y="95" text-anchor="middle" font-size="12" fill="#333">Shannon Diversity: ~5x higher</text>
                        <text x="200" y="110" text-anchor="middle" font-size="10" fill="#666">More uniform distribution</text>
                        <text x="200" y="125" text-anchor="middle" font-size="10" fill="#666">Higher rare allele fraction</text>
                        <text x="200" y="140" text-anchor="middle" font-size="10" fill="#666">Better lineage resolution</text>
                        <text x="200" y="155" text-anchor="middle" font-size="10" fill="#666">65% singleton alleles</text>

                        <!-- Cas9/CARLIN diversity -->
                        <rect x="400" y="50" width="200" height="120" fill="#ffebee" stroke="#e74c3c" stroke-width="2" rx="8"/>
                        <text x="500" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Cas9/CARLIN</text>
                        <text x="500" y="95" text-anchor="middle" font-size="12" fill="#333">Shannon Diversity: baseline</text>
                        <text x="500" y="110" text-anchor="middle" font-size="10" fill="#666">Skewed distribution</text>
                        <text x="500" y="125" text-anchor="middle" font-size="10" fill="#666">Many common alleles</text>
                        <text x="500" y="140" text-anchor="middle" font-size="10" fill="#666">Limited resolution</text>
                        <text x="500" y="155" text-anchor="middle" font-size="10" fill="#666">30% singleton alleles</text>

                        <!-- Frequency distribution visualization -->
                        <text x="350" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Allele Frequency Distributions</text>

                        <!-- DARLIN distribution (more uniform) -->
                        <text x="200" y="220" text-anchor="middle" font-size="12" fill="#27ae60">DARLIN</text>
                        <rect x="120" y="230" width="10" height="40" fill="#27ae60"/>
                        <rect x="135" y="230" width="10" height="35" fill="#27ae60"/>
                        <rect x="150" y="230" width="10" height="30" fill="#27ae60"/>
                        <rect x="165" y="230" width="10" height="25" fill="#27ae60"/>
                        <rect x="180" y="230" width="10" height="20" fill="#27ae60"/>
                        <rect x="195" y="230" width="10" height="18" fill="#27ae60"/>
                        <rect x="210" y="230" width="10" height="15" fill="#27ae60"/>
                        <rect x="225" y="230" width="10" height="12" fill="#27ae60"/>
                        <rect x="240" y="230" width="10" height="10" fill="#27ae60"/>
                        <rect x="255" y="230" width="10" height="8" fill="#27ae60"/>

                        <!-- CARLIN distribution (skewed) -->
                        <text x="500" y="220" text-anchor="middle" font-size="12" fill="#e74c3c">Cas9/CARLIN</text>
                        <rect x="420" y="230" width="10" height="60" fill="#e74c3c"/>
                        <rect x="435" y="230" width="10" height="45" fill="#e74c3c"/>
                        <rect x="450" y="230" width="10" height="25" fill="#e74c3c"/>
                        <rect x="465" y="230" width="10" height="15" fill="#e74c3c"/>
                        <rect x="480" y="230" width="10" height="10" fill="#e74c3c"/>
                        <rect x="495" y="230" width="10" height="8" fill="#e74c3c"/>
                        <rect x="510" y="230" width="10" height="5" fill="#e74c3c"/>
                        <rect x="525" y="230" width="10" height="3" fill="#e74c3c"/>
                        <rect x="540" y="230" width="10" height="2" fill="#e74c3c"/>
                        <rect x="555" y="230" width="10" height="1" fill="#e74c3c"/>

                        <!-- Axes -->
                        <line x1="110" y1="280" x2="270" y2="280" stroke="#666"/>
                        <line x1="110" y1="230" x2="110" y2="280" stroke="#666"/>
                        <text x="190" y="295" text-anchor="middle" font-size="9" fill="#666">Allele Rank</text>
                        <text x="95" y="255" text-anchor="middle" font-size="9" fill="#666" transform="rotate(-90 95 255)">Frequency</text>

                        <line x1="410" y1="280" x2="570" y2="280" stroke="#666"/>
                        <line x1="410" y1="230" x2="410" y2="280" stroke="#666"/>
                        <text x="490" y="295" text-anchor="middle" font-size="9" fill="#666">Allele Rank</text>
                        <text x="395" y="255" text-anchor="middle" font-size="9" fill="#666" transform="rotate(-90 395 255)">Frequency</text>

                        <!-- Key insight -->
                        <rect x="100" y="310" width="500" height="30" fill="#d4edda" stroke="#27ae60" rx="5"/>
                        <text x="350" y="330" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">More uniform distribution = Higher Shannon diversity = Better lineage resolution</text>

                        <defs>
                            <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Singleton Fraction Analysis</h3>
                <p>The singleton fraction (percentage of alleles observed only once) is a key indicator of barcode diversity and system performance.</p>

                <div class="method-box">
                    <span class="method-number">2</span>
                    <strong>Singleton Analysis:</strong> Calculate the fraction of alleles that appear exactly once in the dataset. Higher singleton fractions indicate better barcode diversity and lower homoplasy risk.
                </div>

                <div class="formula-box">
                    <h4>Singleton Fraction Formula</h4>
                    $$\text{Singleton Fraction} = \frac{\text{Number of alleles with count = 1}}{\text{Total number of unique alleles}}$$
                    <p><strong>DARLIN Performance:</strong> ~65% singleton fraction vs ~30% in Cas9/CARLIN</p>
                </div>

                <h3>Good-Turing Estimator for Total Diversity</h3>
                <p>To estimate the total possible barcode diversity, DARLIN analysis uses the Good-Turing estimator:</p>

                <div class="formula-box">
                    <h4>Good-Turing Diversity Estimation</h4>
                    $$\text{Total Alleles} \geq \frac{\text{Observed Alleles}}{1 - \text{Singleton Fraction}}$$
                    <p><strong>DARLIN Calculation:</strong></p>
                    $$\text{Total Alleles} \geq \frac{5.2 \times 10^5}{1 - 0.62} = 1.3 \times 10^6$$
                    <p>This represents a <strong>30-fold increase</strong> over Cas9/CARLIN's 44,000 alleles</p>
                </div>

                <div class="key-insight">
                    <strong>🔑 Key Insight:</strong> The high singleton fraction in DARLIN data indicates that the system is far from saturation, suggesting the true barcode diversity is much higher than observed, potentially reaching the theoretical limit of 10¹⁸ unique combinations.
                </div>
            </div>
        </div>

        <!-- Section 4: Clonal Analysis -->
        <div class="section" id="clonal-analysis">
            <div class="section-header">
                🧬 4. Clonal Analysis Methods
            </div>
            <div class="section-content">
                <h3>Clone ID Assignment Strategy</h3>
                <p>DARLIN integrates information from three independent target arrays to create robust clone identities:</p>

                <div class="svg-container">
                    <svg width="750" height="400" viewBox="0 0 750 400">
                        <rect width="750" height="400" fill="#f8f9fa" rx="10"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Clone ID Assignment Process</text>

                        <!-- Single cell -->
                        <circle cx="375" cy="70" r="20" fill="#3498db"/>
                        <text x="375" y="76" text-anchor="middle" font-size="10" fill="white">Single Cell</text>

                        <!-- Three arrays -->
                        <rect x="150" y="120" width="80" height="40" fill="#e74c3c" rx="5"/>
                        <text x="190" y="135" text-anchor="middle" font-size="10" fill="white">CA Array</text>
                        <text x="190" y="150" text-anchor="middle" font-size="9" fill="white">Allele A1</text>

                        <rect x="335" y="120" width="80" height="40" fill="#27ae60" rx="5"/>
                        <text x="375" y="135" text-anchor="middle" font-size="10" fill="white">TA Array</text>
                        <text x="375" y="150" text-anchor="middle" font-size="9" fill="white">Allele B3</text>

                        <rect x="520" y="120" width="80" height="40" fill="#9b59b6" rx="5"/>
                        <text x="560" y="135" text-anchor="middle" font-size="10" fill="white">RA Array</text>
                        <text x="560" y="150" text-anchor="middle" font-size="9" fill="white">Allele C7</text>

                        <!-- Arrows from cell to arrays -->
                        <path d="M 360 85 L 210 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        <path d="M 375 90 L 375 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        <path d="M 390 85 L 540 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead2)"/>

                        <!-- Integration -->
                        <rect x="300" y="200" width="150" height="50" fill="#f39c12" rx="8"/>
                        <text x="375" y="220" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Clone ID</text>
                        <text x="375" y="235" text-anchor="middle" font-size="10" fill="white">A1_B3_C7</text>

                        <!-- Arrows to integration -->
                        <path d="M 190 160 L 350 200" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        <path d="M 375 160 L 375 200" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        <path d="M 560 160 L 400 200" stroke="#9b59b6" stroke-width="2" marker-end="url(#arrowhead2)"/>

                        <!-- Analysis steps -->
                        <rect x="50" y="280" width="650" height="100" fill="white" stroke="#bdc3c7" rx="8"/>
                        <text x="375" y="300" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Clone Assignment Rules</text>

                        <text x="80" y="320" font-size="11" fill="#333">1. <strong>Single Array:</strong> Use most informative array if others missing</text>
                        <text x="80" y="335" font-size="11" fill="#333">2. <strong>Multiple Arrays:</strong> Concatenate allele IDs from all detected arrays</text>
                        <text x="80" y="350" font-size="11" fill="#333">3. <strong>Independence Check:</strong> Verify editing independence across arrays</text>
                        <text x="80" y="365" font-size="11" fill="#333">4. <strong>Validation:</strong> Cross-reference with allele bank for rarity assessment</text>

                        <defs>
                            <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Clonal Coupling Score</h3>
                <p>A normalized correlation metric to measure how often two cell types appear together within the same clone:</p>

                <div class="formula-box">
                    <h4>Clonal Coupling Score Formula</h4>
                    $$\text{Coupling}(A,B) = \frac{\sum_{c} \min(f_{A,c}, f_{B,c})}{\sqrt{\sum_{c} f_{A,c}^2 \cdot \sum_{c} f_{B,c}^2}}$$
                    <p>Where:</p>
                    <ul>
                        <li>$f_{A,c}$ = fraction of cell type A in clone c</li>
                        <li>$f_{B,c}$ = fraction of cell type B in clone c</li>
                        <li>$c$ = clone index</li>
                    </ul>
                    <p><strong>Range:</strong> 0 (no coupling) to 1 (perfect coupling)</p>
                </div>

                <div class="method-box">
                    <span class="method-number">3</span>
                    <strong>Coupling Analysis:</strong> Calculate pairwise coupling scores between all cell types to identify lineage relationships. High coupling indicates cells frequently appear together in clones, suggesting direct lineage relationships.
                </div>

                <h3>Shared Clone Fraction Analysis</h3>
                <p>For studying cell migration and circulation, DARLIN uses shared clone fraction analysis:</p>

                <div class="formula-box">
                    <h4>Shared Clone Fraction</h4>
                    $$\text{Shared Fraction} = \frac{\text{Clones found in multiple locations}}{\text{Total clones in reference location}}$$
                    <p><strong>Key Findings:</strong></p>
                    <ul>
                        <li>HSCs: ~5% inter-bone sharing (limited circulation)</li>
                        <li>MPPs: ~14% sharing (moderate circulation)</li>
                        <li>MyPs: ~40% sharing (high circulation)</li>
                    </ul>
                </div>

                <div class="key-insight">
                    <strong>💡 Biological Insight:</strong> The increasing shared clone fractions from HSCs to more differentiated cells reveals that stem cells are more location-restricted while progenitors actively circulate between tissues.
                </div>
            </div>
        </div>

        <!-- Section 5: Computational Methods -->
        <div class="section" id="computational">
            <div class="section-header">
                💻 5. Computational Approaches
            </div>
            <div class="section-content">
                <h3>CoSpar: Coherent and Sparse Lineage Dynamics</h3>
                <p>CoSpar is a computational method developed by the authors to infer early cell fate biases by integrating transcriptomic and lineage information.</p>

                <div class="svg-container">
                    <svg width="700" height="400" viewBox="0 0 700 400">
                        <rect width="700" height="400" fill="#f8f9fa" rx="10"/>

                        <text x="350" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">CoSpar Algorithm Workflow</text>

                        <!-- Input data -->
                        <rect x="50" y="50" width="120" height="60" fill="#3498db" rx="8"/>
                        <text x="110" y="70" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Transcriptomic</text>
                        <text x="110" y="85" text-anchor="middle" font-size="11" fill="white">Data</text>
                        <text x="110" y="100" text-anchor="middle" font-size="9" fill="white">Gene expression</text>

                        <rect x="50" y="130" width="120" height="60" fill="#e74c3c" rx="8"/>
                        <text x="110" y="150" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Lineage</text>
                        <text x="110" y="165" text-anchor="middle" font-size="11" fill="white">Data</text>
                        <text x="110" y="180" text-anchor="middle" font-size="9" fill="white">Clone relationships</text>

                        <!-- CoSpar processing -->
                        <rect x="250" y="90" width="150" height="80" fill="#f39c12" rx="8"/>
                        <text x="325" y="115" text-anchor="middle" font-size="14" font-weight="bold" fill="white">CoSpar</text>
                        <text x="325" y="130" text-anchor="middle" font-size="10" fill="white">Algorithm</text>
                        <text x="325" y="145" text-anchor="middle" font-size="9" fill="white">• Coherent dynamics</text>
                        <text x="325" y="158" text-anchor="middle" font-size="9" fill="white">• Sparse optimization</text>

                        <!-- Outputs -->
                        <rect x="480" y="50" width="120" height="60" fill="#27ae60" rx="8"/>
                        <text x="540" y="70" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Fate</text>
                        <text x="540" y="85" text-anchor="middle" font-size="11" fill="white">Probabilities</text>
                        <text x="540" y="100" text-anchor="middle" font-size="9" fill="white">P(cell → fate)</text>

                        <rect x="480" y="130" width="120" height="60" fill="#9b59b6" rx="8"/>
                        <text x="540" y="150" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Trajectory</text>
                        <text x="540" y="165" text-anchor="middle" font-size="11" fill="white">Inference</text>
                        <text x="540" y="180" text-anchor="middle" font-size="9" fill="white">Pseudotime paths</text>

                        <!-- Arrows -->
                        <path d="M 170 80 L 240 110" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead3)"/>
                        <path d="M 170 160 L 240 140" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead3)"/>
                        <path d="M 400 110 L 470 80" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead3)"/>
                        <path d="M 400 140 L 470 160" stroke="#9b59b6" stroke-width="3" marker-end="url(#arrowhead3)"/>

                        <!-- Algorithm details -->
                        <rect x="100" y="220" width="500" height="150" fill="white" stroke="#bdc3c7" rx="10"/>
                        <text x="350" y="245" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">CoSpar Key Features</text>

                        <text x="120" y="270" font-size="12" font-weight="bold" fill="#f39c12">Coherent Dynamics:</text>
                        <text x="120" y="285" font-size="10" fill="#333">• Assumes smooth transitions in gene expression space</text>
                        <text x="120" y="300" font-size="10" fill="#333">• Leverages transcriptomic similarity for fate prediction</text>

                        <text x="120" y="320" font-size="12" font-weight="bold" fill="#f39c12">Sparse Optimization:</text>
                        <text x="120" y="335" font-size="10" fill="#333">• Minimizes complexity while preserving lineage constraints</text>
                        <text x="120" y="350" font-size="10" fill="#333">• Robust to noise and missing data</text>

                        <defs>
                            <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <div class="method-box">
                    <span class="method-number">4</span>
                    <strong>CoSpar Application:</strong> Use CoSpar to predict cell fate probabilities by integrating clonal relationships with transcriptomic states. This reveals early fate biases that are not apparent from gene expression alone.
                </div>

                <h3>UMAP Dimensionality Reduction</h3>
                <p>UMAP (Uniform Manifold Approximation and Projection) is used for visualizing high-dimensional transcriptomic data:</p>

                <div class="workflow-step">
                    <strong>UMAP Parameters for DARLIN:</strong>
                    <ul>
                        <li><strong>Input:</strong> Normalized gene expression matrix</li>
                        <li><strong>Features:</strong> Highly variable genes</li>
                        <li><strong>Neighbors:</strong> Optimized for cell type resolution</li>
                        <li><strong>Output:</strong> 2D embedding for visualization</li>
                    </ul>
                </div>

                <h3>Pseudotime Trajectory Inference</h3>
                <p>Pseudotime analysis orders cells along developmental trajectories to study fate transitions:</p>

                <div class="formula-box">
                    <h4>Pseudotime Analysis Steps</h4>
                    <ol>
                        <li><strong>Root Selection:</strong> Identify starting cell population (e.g., HSCs)</li>
                        <li><strong>Trajectory Construction:</strong> Build paths through transcriptomic space</li>
                        <li><strong>Pseudotime Assignment:</strong> Order cells along trajectories</li>
                        <li><strong>Fate Splitting:</strong> Identify branch points and fate decisions</li>
                    </ol>
                    <p><strong>Application:</strong> Split Meg-biased HSCs into early vs. late populations based on pseudotime</p>
                </div>

                <div class="svg-container">
                    <svg width="600" height="300" viewBox="0 0 600 300">
                        <rect width="600" height="300" fill="#f8f9fa" rx="10"/>

                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Pseudotime Trajectory Example</text>

                        <!-- HSC start -->
                        <circle cx="100" cy="150" r="15" fill="#e74c3c"/>
                        <text x="100" y="156" text-anchor="middle" font-size="10" fill="white">HSC</text>

                        <!-- Trajectory path -->
                        <path d="M 115 150 Q 200 100 300 150" stroke="#3498db" stroke-width="4" fill="none"/>
                        <path d="M 300 150 Q 400 120 500 100" stroke="#27ae60" stroke-width="4" fill="none"/>
                        <path d="M 300 150 Q 400 180 500 200" stroke="#9b59b6" stroke-width="4" fill="none"/>

                        <!-- Branch point -->
                        <circle cx="300" cy="150" r="8" fill="#f39c12"/>
                        <text x="300" y="170" text-anchor="middle" font-size="9" fill="#333">Branch Point</text>

                        <!-- End fates -->
                        <circle cx="500" cy="100" r="12" fill="#27ae60"/>
                        <text x="500" y="106" text-anchor="middle" font-size="9" fill="white">Meg</text>

                        <circle cx="500" cy="200" r="12" fill="#9b59b6"/>
                        <text x="500" y="206" text-anchor="middle" font-size="9" fill="white">Other</text>

                        <!-- Pseudotime axis -->
                        <line x1="80" y1="250" x2="520" y2="250" stroke="#666" stroke-width="2"/>
                        <text x="300" y="270" text-anchor="middle" font-size="12" fill="#666">Pseudotime →</text>

                        <!-- Time markers -->
                        <line x1="100" y1="245" x2="100" y2="255" stroke="#666"/>
                        <text x="100" y="240" text-anchor="middle" font-size="9" fill="#666">Early</text>

                        <line x1="300" y1="245" x2="300" y2="255" stroke="#666"/>
                        <text x="300" y="240" text-anchor="middle" font-size="9" fill="#666">Decision</text>

                        <line x1="500" y1="245" x2="500" y2="255" stroke="#666"/>
                        <text x="500" y="240" text-anchor="middle" font-size="9" fill="#666">Mature</text>
                    </svg>
                </div>

                <div class="key-insight">
                    <strong>🔑 CoSpar Discovery:</strong> CoSpar revealed that megakaryocytes originate specifically from a subset of HSCs, while monocytes predominantly arise from LMPPs - insights that were not detectable with lower-resolution lineage tracing systems.
                </div>
            </div>
        </div>

        <!-- Section 6: Multi-omics Integration -->
        <div class="section" id="multi-omics">
            <div class="section-header">
                🔬 6. Multi-omics Integration with Camellia-seq
            </div>
            <div class="section-content">
                <h3>Camellia-seq Data Processing Pipeline</h3>
                <p>Camellia-seq simultaneously profiles four molecular modalities in single cells, requiring specialized analysis approaches:</p>

                <div class="svg-container">
                    <svg width="750" height="450" viewBox="0 0 750 450">
                        <rect width="750" height="450" fill="#f8f9fa" rx="10"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Camellia-seq Analysis Pipeline</text>

                        <!-- Single cell input -->
                        <circle cx="375" cy="70" r="20" fill="#34495e"/>
                        <text x="375" y="76" text-anchor="middle" font-size="10" fill="white">Single Cell</text>

                        <!-- Cell fractionation -->
                        <rect x="200" y="120" width="100" height="50" fill="#e74c3c" rx="8"/>
                        <text x="250" y="140" text-anchor="middle" font-size="11" fill="white">Cytoplasmic</text>
                        <text x="250" y="155" text-anchor="middle" font-size="11" fill="white">Fraction</text>

                        <rect x="450" y="120" width="100" height="50" fill="#27ae60" rx="8"/>
                        <text x="500" y="140" text-anchor="middle" font-size="11" fill="white">Nuclear</text>
                        <text x="500" y="155" text-anchor="middle" font-size="11" fill="white">Fraction</text>

                        <!-- Processing paths -->
                        <rect x="150" y="200" width="80" height="40" fill="#3498db" rx="5"/>
                        <text x="190" y="215" text-anchor="middle" font-size="9" fill="white">RNA-seq</text>
                        <text x="190" y="228" text-anchor="middle" font-size="9" fill="white">Expression</text>

                        <rect x="250" y="200" width="80" height="40" fill="#f39c12" rx="5"/>
                        <text x="290" y="215" text-anchor="middle" font-size="9" fill="white">Lineage</text>
                        <text x="290" y="228" text-anchor="middle" font-size="9" fill="white">Barcodes</text>

                        <rect x="420" y="200" width="80" height="40" fill="#9b59b6" rx="5"/>
                        <text x="460" y="215" text-anchor="middle" font-size="9" fill="white">DNA</text>
                        <text x="460" y="228" text-anchor="middle" font-size="9" fill="white">Methylation</text>

                        <rect x="520" y="200" width="80" height="40" fill="#e67e22" rx="5"/>
                        <text x="560" y="215" text-anchor="middle" font-size="9" fill="white">Chromatin</text>
                        <text x="560" y="228" text-anchor="middle" font-size="9" fill="white">Access.</text>

                        <!-- Arrows -->
                        <path d="M 355 85 L 270 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 395 85 L 480 120" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>

                        <path d="M 230 170 L 190 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 270 170 L 290 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 480 170 L 460 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 520 170 L 560 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead4)"/>

                        <!-- Integration -->
                        <rect x="300" y="280" width="150" height="50" fill="#2c3e50" rx="8"/>
                        <text x="375" y="300" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Integrated</text>
                        <text x="375" y="315" text-anchor="middle" font-size="12" fill="white">Analysis</text>

                        <!-- Integration arrows -->
                        <path d="M 190 240 L 340 280" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 290 240 L 360 280" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 460 240 L 390 280" stroke="#9b59b6" stroke-width="2" marker-end="url(#arrowhead4)"/>
                        <path d="M 560 240 L 410 280" stroke="#e67e22" stroke-width="2" marker-end="url(#arrowhead4)"/>

                        <!-- Quality metrics -->
                        <rect x="50" y="360" width="650" height="70" fill="#e8f5e8" stroke="#27ae60" rx="8"/>
                        <text x="375" y="380" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Camellia-seq Quality Metrics</text>
                        <text x="150" y="400" text-anchor="middle" font-size="10" fill="#333">~100K UMIs/cell</text>
                        <text x="300" y="400" text-anchor="middle" font-size="10" fill="#333">~3K genes/cell</text>
                        <text x="450" y="400" text-anchor="middle" font-size="10" fill="#333">70% promoter coverage</text>
                        <text x="600" y="400" text-anchor="middle" font-size="10" fill="#333">50% rare barcodes</text>

                        <text x="150" y="415" text-anchor="middle" font-size="10" fill="#333">Expression</text>
                        <text x="300" y="415" text-anchor="middle" font-size="10" fill="#333">Detection</text>
                        <text x="450" y="415" text-anchor="middle" font-size="10" fill="#333">Epigenomic</text>
                        <text x="600" y="415" text-anchor="middle" font-size="10" fill="#333">Lineage</text>

                        <defs>
                            <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Cross-Modal Correlation Analysis</h3>
                <p>Camellia-seq enables analysis of relationships between different molecular modalities:</p>

                <div class="method-box">
                    <span class="method-number">5</span>
                    <strong>Epigenomic Validation:</strong> Confirm that chromatin accessibility anti-correlates with DNA methylation and correlates with gene expression, validating the technical quality of multi-omics measurements.
                </div>

                <div class="formula-box">
                    <h4>TSS Profile Analysis</h4>
                    <p>Analyze molecular profiles around transcription start sites (TSS):</p>
                    $$\text{Profile}_{TSS}(d) = \frac{1}{N} \sum_{g=1}^{N} \text{Signal}_g(\text{TSS}_g + d)$$
                    <p>Where:</p>
                    <ul>
                        <li>$d$ = distance from TSS (-1kb to +1kb)</li>
                        <li>$N$ = number of genes</li>
                        <li>$\text{Signal}_g$ = methylation or accessibility signal for gene $g$</li>
                    </ul>
                    <p><strong>Expected Pattern:</strong> DNA methylation drops at TSS, chromatin accessibility peaks at TSS</p>
                </div>

                <table class="analysis-table">
                    <thead>
                        <tr>
                            <th>Modality</th>
                            <th>Processing Method</th>
                            <th>Key Metrics</th>
                            <th>Quality Threshold</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Gene Expression</strong></td>
                            <td>Modified STRT-seq</td>
                            <td>UMIs, genes detected</td>
                            <td>>1000 UMIs, >500 genes</td>
                        </tr>
                        <tr>
                            <td><strong>Lineage Barcodes</strong></td>
                            <td>Targeted amplification</td>
                            <td>Barcode detection, editing</td>
                            <td>Detected + edited + rare</td>
                        </tr>
                        <tr>
                            <td><strong>DNA Methylation</strong></td>
                            <td>Bisulfite sequencing</td>
                            <td>CpG coverage, methylation %</td>
                            <td>>1 read per CpG site</td>
                        </tr>
                        <tr>
                            <td><strong>Chromatin Access.</strong></td>
                            <td>GpC methyltransferase</td>
                            <td>GpC coverage, accessibility</td>
                            <td>>3 reads per GpC site</td>
                        </tr>
                    </tbody>
                </table>

                <div class="key-insight">
                    <strong>💡 Integration Advantage:</strong> Multi-omics integration reveals that epigenetic changes often precede transcriptomic changes, providing earlier detection of cell fate decisions than gene expression alone.
                </div>
            </div>
        </div>

        <!-- Section 7: Statistical Testing -->
        <div class="section" id="statistical">
            <div class="section-header">
                📈 7. Statistical Testing and Validation
            </div>
            <div class="section-content">
                <h3>Robustness Testing Framework</h3>
                <p>DARLIN analysis employs multiple validation strategies to ensure the reliability of biological conclusions:</p>

                <div class="svg-container">
                    <svg width="700" height="400" viewBox="0 0 700 400">
                        <rect width="700" height="400" fill="#f8f9fa" rx="10"/>

                        <text x="350" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Statistical Validation Strategy</text>

                        <!-- Central analysis -->
                        <circle cx="350" cy="100" r="30" fill="#3498db"/>
                        <text x="350" y="95" text-anchor="middle" font-size="10" fill="white">Primary</text>
                        <text x="350" y="108" text-anchor="middle" font-size="10" fill="white">Analysis</text>

                        <!-- Validation approaches -->
                        <rect x="100" y="50" width="120" height="50" fill="#27ae60" rx="8"/>
                        <text x="160" y="70" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Down-sampling</text>
                        <text x="160" y="85" text-anchor="middle" font-size="9" fill="white">Validation</text>

                        <rect x="480" y="50" width="120" height="50" fill="#f39c12" rx="8"/>
                        <text x="540" y="70" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Cross-dataset</text>
                        <text x="540" y="85" text-anchor="middle" font-size="9" fill="white">Comparison</text>

                        <rect x="100" y="150" width="120" height="50" fill="#9b59b6" rx="8"/>
                        <text x="160" y="170" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Parameter</text>
                        <text x="160" y="185" text-anchor="middle" font-size="9" fill="white">Sensitivity</text>

                        <rect x="480" y="150" width="120" height="50" fill="#e74c3c" rx="8"/>
                        <text x="540" y="170" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Statistical</text>
                        <text x="540" y="185" text-anchor="middle" font-size="9" fill="white">Testing</text>

                        <!-- Arrows -->
                        <path d="M 320 85 L 220 75" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 380 85 L 480 75" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 320 115 L 220 175" stroke="#9b59b6" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 380 115 L 480 175" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead5)"/>

                        <!-- Detailed methods -->
                        <rect x="50" y="240" width="600" height="140" fill="white" stroke="#bdc3c7" rx="10"/>
                        <text x="350" y="260" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Validation Methods Detail</text>

                        <text x="80" y="285" font-size="12" font-weight="bold" fill="#27ae60">1. Down-sampling Validation:</text>
                        <text x="100" y="300" font-size="10" fill="#333">• Randomly subsample UMIs to test robustness</text>
                        <text x="100" y="315" font-size="10" fill="#333">• Verify shared clone fractions remain stable</text>

                        <text x="80" y="335" font-size="12" font-weight="bold" fill="#f39c12">2. Cross-dataset Comparison:</text>
                        <text x="100" y="350" font-size="10" fill="#333">• Compare DARLIN vs Cas9/CARLIN performance</text>
                        <text x="100" y="365" font-size="10" fill="#333">• Validate improvements across multiple datasets</text>

                        <defs>
                            <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Statistical Testing Approaches</h3>

                <div class="method-box">
                    <span class="method-number">6</span>
                    <strong>T-test for Migration Analysis:</strong> Use t-tests to compare shared clone fractions between different cell types and developmental stages (p < 0.05 significance threshold).
                </div>

                <div class="method-box">
                    <span class="method-number">7</span>
                    <strong>Linear Regression for Saturation:</strong> Fit linear models to assess whether allele discovery is saturated. High R² values (>0.97) with linear relationships indicate continued discovery potential.
                </div>

                <div class="formula-box">
                    <h4>Robustness Testing Parameters</h4>
                    <p><strong>1. Mutational Complexity Thresholds:</strong></p>
                    $$\text{Complexity} = \sum_{i=1}^{10} (\text{insertions}_i + \text{deletions}_i)$$

                    <p><strong>2. UMI Down-sampling:</strong></p>
                    $$\text{Sampled UMIs} = \text{Original UMIs} \times \text{sampling fraction}$$

                    <p><strong>3. Read Cutoff Sensitivity:</strong></p>
                    $$\text{Allele Detection} = f(\text{min reads}, \text{quality score})$$
                </div>

                <h3>Cross-Validation Strategies</h3>
                <p>Multiple validation approaches ensure the robustness of DARLIN findings:</p>

                <table class="analysis-table">
                    <thead>
                        <tr>
                            <th>Validation Type</th>
                            <th>Method</th>
                            <th>Purpose</th>
                            <th>Key Result</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Down-sampling</strong></td>
                            <td>Random UMI subsampling</td>
                            <td>Test robustness to sequencing depth</td>
                            <td>Stable shared clone fractions</td>
                        </tr>
                        <tr>
                            <td><strong>Complexity Filtering</strong></td>
                            <td>Variable mutation thresholds</td>
                            <td>Assess impact of allele complexity</td>
                            <td>Robust to filtering parameters</td>
                        </tr>
                        <tr>
                            <td><strong>Read Cutoffs</strong></td>
                            <td>Variable minimum read counts</td>
                            <td>Test sensitivity to detection limits</td>
                            <td>Consistent across cutoffs</td>
                        </tr>
                        <tr>
                            <td><strong>Cross-system</strong></td>
                            <td>DARLIN vs Cas9/CARLIN</td>
                            <td>Validate performance improvements</td>
                            <td>6x better cell coverage</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Biological Validation</h3>
                <div class="workflow-step">
                    <strong>Literature Concordance:</strong> Validate DARLIN discoveries against known biology:
                    <ul>
                        <li>HSC→Megakaryocyte direct pathway (confirmed with literature)</li>
                        <li>LMPP→Monocyte bias (matches previous reports)</li>
                        <li>HSC circulation rates (consistent with parabiosis studies)</li>
                    </ul>
                </div>

                <div class="key-insight">
                    <strong>🎯 Validation Success:</strong> All major DARLIN findings were robust across multiple validation approaches and consistent with independent biological evidence, demonstrating the reliability of the analytical framework.
                </div>

                <h3>Analysis Software and Tools</h3>
                <div class="code-block">
# Key Analysis Tools Used in DARLIN Study
- scRNA-seq: Seurat, Scanpy
- Lineage analysis: Custom DARLIN pipeline
- CoSpar: Fate prediction algorithm
- UMAP: Dimensionality reduction
- Statistical testing: R/Python scipy
- Multi-omics: Custom Camellia-seq pipeline
                </div>

                <div class="formula-box">
                    <h4>Summary of Key Metrics</h4>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Shannon Diversity</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">$2^H$ where $H = -\sum p_i \log_2 p_i$</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Singleton Fraction</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">$\frac{\text{Alleles with count=1}}{\text{Total alleles}}$</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Clonal Coupling</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">$\frac{\sum_c \min(f_{A,c}, f_{B,c})}{\sqrt{\sum_c f_{A,c}^2 \cdot \sum_c f_{B,c}^2}}$</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Homoplasy Probability</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">$1 - \left(\frac{f_{intrinsic}}{f_{observed}}\right)^n$</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding: 40px; color: white; background: rgba(0,0,0,0.1); border-radius: 15px; margin-top: 30px;">
            <p style="font-size: 1.1em; font-weight: bold;">DARLIN Data Analysis Tutorial</p>
            <p>Based on: Li et al. (2023) "A mouse model with high clonal barcode diversity for joint lineage, transcriptomic, and epigenomic profiling in single cells"</p>
            <p style="margin-top: 15px; font-size: 0.9em;">Comprehensive guide to advanced single-cell lineage tracing analysis</p>
        </div>
    </div>
</body>
</html>
