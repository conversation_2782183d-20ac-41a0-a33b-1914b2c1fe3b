<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MosaicLineage - Code Structure and Algorithms Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #9b59b6;
            --dark-color: #34495e;
            --light-bg: #ecf0f1;
            --text-color: #2c3e50;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
            color: white;
            padding: 50px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 25px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 3.2em;
            margin-bottom: 15px;
            text-shadow: 3px 3px 8px rgba(0,0,0,0.4);
        }

        .header p {
            font-size: 1.5em;
            opacity: 0.9;
        }

        .outline {
            background: white;
            padding: 45px;
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            margin-bottom: 30px;
            border-left: 10px solid var(--info-color);
        }

        .outline h2 {
            color: var(--primary-color);
            margin-bottom: 30px;
            font-size: 2.4em;
        }

        .outline ol {
            padding-left: 35px;
        }

        .outline li {
            margin-bottom: 18px;
            font-size: 1.3em;
        }

        .outline li ul {
            margin-top: 12px;
            padding-left: 35px;
        }

        .outline li ul li {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 10px;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-left: 10px solid var(--accent-color);
        }

        .section-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
            padding: 35px 45px;
            font-size: 2em;
            font-weight: bold;
        }

        .section-content {
            padding: 45px;
        }

        .module-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 8px solid var(--success-color);
            padding: 30px;
            margin: 30px 0;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .module-number {
            background: var(--success-color);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            font-size: 1.3em;
        }

        .algorithm-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 3px solid var(--warning-color);
            padding: 30px;
            margin: 30px 0;
            border-radius: 15px;
            border-left: 8px solid var(--warning-color);
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 30px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            margin: 25px 0;
            overflow-x: auto;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .function-signature {
            background: #34495e;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            border-left: 5px solid var(--secondary-color);
        }

        .architecture-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .architecture-table th {
            background: var(--primary-color);
            color: white;
            padding: 25px;
            text-align: left;
            font-size: 1.2em;
        }

        .architecture-table td {
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-color);
        }

        .architecture-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .svg-container {
            text-align: center;
            margin: 40px 0;
            padding: 35px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .key-insight {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left: 8px solid var(--success-color);
            padding: 30px;
            margin: 30px 0;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .algorithm-flow {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 8px solid var(--secondary-color);
            padding: 30px;
            margin: 25px 0;
            border-radius: 15px;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            max-width: 280px;
            z-index: 1000;
        }

        .navigation h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .navigation ul {
            list-style: none;
        }

        .navigation li {
            margin-bottom: 12px;
        }

        .navigation a {
            color: var(--secondary-color);
            text-decoration: none;
            font-size: 1.05em;
            display: block;
            padding: 12px;
            border-radius: 10px;
            transition: all 0.3s;
        }

        .navigation a:hover {
            background: var(--light-bg);
            transform: translateX(8px);
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <h3>Code Navigation</h3>
        <ul>
            <li><a href="#overview">Package Overview</a></li>
            <li><a href="#architecture">Code Architecture</a></li>
            <li><a href="#core-modules">Core Modules</a></li>
            <li><a href="#algorithms">Key Algorithms</a></li>
            <li><a href="#data-structures">Data Structures</a></li>
            <li><a href="#analysis-pipeline">Analysis Pipeline</a></li>
            <li><a href="#visualization">Visualization</a></li>
        </ul>
    </div>

    <div class="container">
        <div class="header">
            <h1>MosaicLineage</h1>
            <p>Code Structure and Algorithms Deep Dive</p>
        </div>

        <div class="outline">
            <h2>🧬 Tutorial Outline</h2>
            <ol>
                <li><strong>Package Overview and Purpose</strong>
                    <ul>
                        <li>MosaicLineage package architecture</li>
                        <li>Integration with DARLIN and CARLIN systems</li>
                        <li>Core dependencies and design philosophy</li>
                        <li>Relationship to CoSpar and Scanpy ecosystems</li>
                    </ul>
                </li>
                <li><strong>Code Architecture and Module Structure</strong>
                    <ul>
                        <li>Module hierarchy and dependencies</li>
                        <li>Core modules: DARLIN, larry, lineage, util</li>
                        <li>Analysis and plotting modules</li>
                        <li>Configuration and settings management</li>
                    </ul>
                </li>
                <li><strong>Core Modules Deep Dive</strong>
                    <ul>
                        <li>DARLIN.py: CARLIN-specific operations</li>
                        <li>larry.py: Sequence denoising and QC</li>
                        <li>lineage.py: Downstream allele analysis</li>
                        <li>util.py: Mathematical and utility functions</li>
                    </ul>
                </li>
                <li><strong>Key Algorithms and Mathematical Methods</strong>
                    <ul>
                        <li>Sequence consensus algorithms</li>
                        <li>UMI clustering and denoising</li>
                        <li>Shannon diversity calculations</li>
                        <li>Clonal coupling analysis</li>
                        <li>Shared clone fraction computation</li>
                    </ul>
                </li>
                <li><strong>Data Structures and Formats</strong>
                    <ul>
                        <li>AnnData integration for single-cell analysis</li>
                        <li>DataFrame structures for allele data</li>
                        <li>Sparse matrix representations</li>
                        <li>Clone-by-cell and allele-by-mutation matrices</li>
                    </ul>
                </li>
                <li><strong>Analysis Pipeline Implementation</strong>
                    <ul>
                        <li>Raw data processing workflow</li>
                        <li>Quality control and filtering steps</li>
                        <li>Multi-sample integration strategies</li>
                        <li>Statistical analysis and validation</li>
                    </ul>
                </li>
                <li><strong>Visualization and Plotting Framework</strong>
                    <ul>
                        <li>Plotting module architecture</li>
                        <li>Custom visualization functions</li>
                        <li>Integration with matplotlib and seaborn</li>
                        <li>Interactive and publication-ready outputs</li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 1: Package Overview -->
        <div class="section" id="overview">
            <div class="section-header">
                📦 1. Package Overview and Purpose
            </div>
            <div class="section-content">
                <h3>What is MosaicLineage?</h3>
                <p><strong>MosaicLineage</strong> is a comprehensive Python package designed to analyze lineage tracing data from different experimental systems, with specialized support for DARLIN and CARLIN technologies. It provides a unified framework for processing, analyzing, and visualizing single-cell and bulk lineage tracing datasets.</p>

                <div class="svg-container">
                    <svg width="800" height="450" viewBox="0 0 800 450">
                        <rect width="800" height="450" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#2c3e50">MosaicLineage Package Ecosystem</text>

                        <!-- Core package -->
                        <circle cx="400" cy="120" r="50" fill="#2c3e50"/>
                        <text x="400" y="115" text-anchor="middle" font-size="14" font-weight="bold" fill="white">MosaicLineage</text>
                        <text x="400" y="130" text-anchor="middle" font-size="10" fill="white">v0.2.0</text>

                        <!-- External dependencies -->
                        <rect x="150" y="60" width="120" height="40" fill="#3498db" rx="8"/>
                        <text x="210" y="75" text-anchor="middle" font-size="11" fill="white">CoSpar</text>
                        <text x="210" y="90" text-anchor="middle" font-size="9" fill="white">Fate prediction</text>

                        <rect x="530" y="60" width="120" height="40" fill="#27ae60" rx="8"/>
                        <text x="590" y="75" text-anchor="middle" font-size="11" fill="white">Scanpy</text>
                        <text x="590" y="90" text-anchor="middle" font-size="9" fill="white">Single-cell analysis</text>

                        <rect x="150" y="180" width="120" height="40" fill="#f39c12" rx="8"/>
                        <text x="210" y="195" text-anchor="middle" font-size="11" fill="white">BioPython</text>
                        <text x="210" y="210" text-anchor="middle" font-size="9" fill="white">Sequence analysis</text>

                        <rect x="530" y="180" width="120" height="40" fill="#9b59b6" rx="8"/>
                        <text x="590" y="195" text-anchor="middle" font-size="11" fill="white">UMI-tools</text>
                        <text x="590" y="210" text-anchor="middle" font-size="9" fill="white">UMI clustering</text>

                        <!-- Data science stack -->
                        <rect x="300" y="240" width="80" height="30" fill="#e74c3c" rx="5"/>
                        <text x="340" y="260" text-anchor="middle" font-size="10" fill="white">NumPy</text>

                        <rect x="390" y="240" width="80" height="30" fill="#e74c3c" rx="5"/>
                        <text x="430" y="260" text-anchor="middle" font-size="10" fill="white">Pandas</text>

                        <rect x="480" y="240" width="80" height="30" fill="#e74c3c" rx="5"/>
                        <text x="520" y="260" text-anchor="middle" font-size="10" fill="white">SciPy</text>

                        <!-- Arrows -->
                        <path d="M 350 100 L 270 80" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 450 100 L 530 80" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 350 140 L 270 200" stroke="#f39c12" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 450 140 L 530 200" stroke="#9b59b6" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <path d="M 400 170 L 400 240" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead)"/>

                        <!-- Applications -->
                        <rect x="50" y="320" width="700" height="100" fill="white" stroke="#bdc3c7" stroke-width="2" rx="12"/>
                        <text x="400" y="345" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Primary Applications</text>

                        <rect x="80" y="360" width="140" height="50" fill="#e8f5e8" stroke="#27ae60" rx="8"/>
                        <text x="150" y="380" text-anchor="middle" font-size="11" fill="#27ae60">DARLIN Analysis</text>
                        <text x="150" y="395" text-anchor="middle" font-size="9" fill="#333">3-array processing</text>

                        <rect x="240" y="360" width="140" height="50" fill="#e3f2fd" stroke="#3498db" rx="8"/>
                        <text x="310" y="380" text-anchor="middle" font-size="11" fill="#3498db">CARLIN Analysis</text>
                        <text x="310" y="395" text-anchor="middle" font-size="9" fill="#333">Legacy support</text>

                        <rect x="400" y="360" width="140" height="50" fill="#fff3cd" stroke="#f39c12" rx="8"/>
                        <text x="470" y="380" text-anchor="middle" font-size="11" fill="#f39c12">Multi-omics</text>
                        <text x="470" y="395" text-anchor="middle" font-size="9" fill="#333">Camellia-seq</text>

                        <rect x="560" y="360" width="140" height="50" fill="#f3e5f5" stroke="#9b59b6" rx="8"/>
                        <text x="630" y="380" text-anchor="middle" font-size="11" fill="#9b59b6">Simulation</text>
                        <text x="630" y="395" text-anchor="middle" font-size="9" fill="#333">Power law models</text>

                        <defs>
                            <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Design Philosophy</h3>
                <div class="key-insight">
                    <strong>🎯 Core Design Principles:</strong>
                    <ul>
                        <li><strong>Modularity:</strong> Separate modules for different aspects of lineage analysis</li>
                        <li><strong>Flexibility:</strong> Support for multiple experimental protocols and data types</li>
                        <li><strong>Integration:</strong> Seamless compatibility with existing single-cell analysis tools</li>
                        <li><strong>Reproducibility:</strong> Standardized functions with consistent interfaces</li>
                    </ul>
                </div>

                <h3>Package Dependencies</h3>
                <table class="architecture-table">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Package</th>
                            <th>Purpose</th>
                            <th>Key Features Used</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Data Science</strong></td>
                            <td>NumPy, Pandas, SciPy</td>
                            <td>Core data manipulation</td>
                            <td>Arrays, DataFrames, sparse matrices</td>
                        </tr>
                        <tr>
                            <td><strong>Single-cell</strong></td>
                            <td>Scanpy, AnnData</td>
                            <td>Single-cell data structures</td>
                            <td>AnnData objects, embeddings</td>
                        </tr>
                        <tr>
                            <td><strong>Lineage Analysis</strong></td>
                            <td>CoSpar</td>
                            <td>Fate prediction algorithms</td>
                            <td>Coherent sparse optimization</td>
                        </tr>
                        <tr>
                            <td><strong>Sequence Analysis</strong></td>
                            <td>BioPython, UMI-tools</td>
                            <td>Sequence processing</td>
                            <td>Alignment, UMI clustering</td>
                        </tr>
                        <tr>
                            <td><strong>Visualization</strong></td>
                            <td>Matplotlib, Seaborn</td>
                            <td>Plotting and visualization</td>
                            <td>Statistical plots, heatmaps</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Integration with DARLIN Ecosystem</h3>
                <div class="algorithm-flow">
                    <strong>🔗 Ecosystem Integration:</strong> MosaicLineage serves as the Python analysis backend for DARLIN data, complementing the Snakemake pipeline and Custom CARLIN MATLAB components. It provides the computational foundation for advanced lineage analysis methods described in the Li et al. 2023 paper.
                </div>
            </div>
        </div>

        <!-- Section 2: Code Architecture -->
        <div class="section" id="architecture">
            <div class="section-header">
                🏗️ 2. Code Architecture and Module Structure
            </div>
            <div class="section-content">
                <h3>Module Hierarchy and Dependencies</h3>
                <p>MosaicLineage follows a modular architecture with clear separation of concerns:</p>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <rect width="800" height="500" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">MosaicLineage Module Architecture</text>

                        <!-- Core modules layer -->
                        <rect x="50" y="60" width="700" height="100" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="12"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Core Analysis Modules</text>

                        <rect x="80" y="105" width="120" height="45" fill="#27ae60" rx="8"/>
                        <text x="140" y="125" text-anchor="middle" font-size="11" fill="white">DARLIN.py</text>
                        <text x="140" y="140" text-anchor="middle" font-size="9" fill="white">CARLIN operations</text>

                        <rect x="220" y="105" width="120" height="45" fill="#27ae60" rx="8"/>
                        <text x="280" y="125" text-anchor="middle" font-size="11" fill="white">larry.py</text>
                        <text x="280" y="140" text-anchor="middle" font-size="9" fill="white">Sequence QC</text>

                        <rect x="360" y="105" width="120" height="45" fill="#27ae60" rx="8"/>
                        <text x="420" y="125" text-anchor="middle" font-size="11" fill="white">lineage.py</text>
                        <text x="420" y="140" text-anchor="middle" font-size="9" fill="white">Allele analysis</text>

                        <rect x="500" y="105" width="120" height="45" fill="#27ae60" rx="8"/>
                        <text x="560" y="125" text-anchor="middle" font-size="11" fill="white">util.py</text>
                        <text x="560" y="140" text-anchor="middle" font-size="9" fill="white">Utilities</text>

                        <!-- Analysis layer -->
                        <rect x="50" y="180" width="700" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="400" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Analysis and Workflow Modules</text>

                        <rect x="150" y="225" width="150" height="25" fill="#3498db" rx="5"/>
                        <text x="225" y="242" text-anchor="middle" font-size="10" fill="white">analysis_script.py</text>

                        <rect x="320" y="225" width="150" height="25" fill="#3498db" rx="5"/>
                        <text x="395" y="242" text-anchor="middle" font-size="10" fill="white">plot_scripts.py</text>

                        <rect x="490" y="225" width="150" height="25" fill="#3498db" rx="5"/>
                        <text x="565" y="242" text-anchor="middle" font-size="10" fill="white">simulate.py</text>

                        <!-- Visualization layer -->
                        <rect x="50" y="280" width="700" height="60" fill="#fff3cd" stroke="#f39c12" stroke-width="3" rx="12"/>
                        <text x="400" y="305" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Visualization and Interface</text>

                        <rect x="250" y="320" width="120" height="25" fill="#f39c12" rx="5"/>
                        <text x="310" y="337" text-anchor="middle" font-size="10" fill="white">plotting.py</text>

                        <rect x="390" y="320" width="120" height="25" fill="#f39c12" rx="5"/>
                        <text x="450" y="337" text-anchor="middle" font-size="10" fill="white">settings.py</text>

                        <!-- Configuration layer -->
                        <rect x="50" y="360" width="700" height="60" fill="#f3e5f5" stroke="#9b59b6" stroke-width="3" rx="12"/>
                        <text x="400" y="385" text-anchor="middle" font-size="16" font-weight="bold" fill="#9b59b6">Configuration and Integration</text>

                        <rect x="300" y="400" width="200" height="25" fill="#9b59b6" rx="5"/>
                        <text x="400" y="417" text-anchor="middle" font-size="10" fill="white">help_functions.py (Unified Interface)</text>

                        <!-- Arrows showing dependencies -->
                        <path d="M 400 170 L 400 180" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>
                        <path d="M 400 270 L 400 280" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>
                        <path d="M 400 350 L 400 360" stroke="#666" stroke-width="3" marker-end="url(#arrowhead1)"/>

                        <defs>
                            <marker id="arrowhead1" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Module Responsibilities</h3>
                <div class="module-box">
                    <span class="module-number">1</span>
                    <strong>DARLIN.py:</strong> Handles CARLIN-specific operations including sequence definitions for CA, TA, and RA arrays, editing efficiency calculations, and raw FASTQ processing for different protocols (scCamellia, sc10xV3).
                </div>

                <div class="module-box">
                    <span class="module-number">2</span>
                    <strong>larry.py:</strong> Provides functions for extracting and denoising meaningful cell barcodes, with extensive QC functions useful for both LARRY and CARLIN protocols, especially for handling sequencing errors in barcodes.
                </div>

                <div class="module-box">
                    <span class="module-number">3</span>
                    <strong>lineage.py:</strong> Contains downstream allele analysis functions including frequency analysis, mutation-allele relationships, Shannon diversity calculations, and coupling analysis.
                </div>

                <div class="module-box">
                    <span class="module-number">4</span>
                    <strong>util.py:</strong> Provides concise utility functions for mathematical operations, sequence manipulation, and data structure transformations.
                </div>

                <h3>Import Structure and Dependencies</h3>
                <div class="function-signature">
# Core imports in help_functions.py (unified interface)
from .analysis_script import *
from .DARLIN import *
from .larry import *
from .lineage import *
from .plot_scripts import *
from .plotting import *
from .settings import *
from .simulate import *
from .util import *
                </div>

                <div class="key-insight">
                    <strong>💡 Architectural Insight:</strong> The package uses a star import pattern in help_functions.py to provide a unified interface, allowing users to access all functionality through a single import while maintaining modular code organization.
                </div>
            </div>
        </div>

        <!-- Section 3: Core Modules -->
        <div class="section" id="core-modules">
            <div class="section-header">
                🔧 3. Core Modules Deep Dive
            </div>
            <div class="section-content">
                <h3>DARLIN.py - CARLIN Operations Module</h3>
                <p>This module provides specialized functions for handling DARLIN's three-array system and various experimental protocols:</p>

                <div class="svg-container">
                    <svg width="750" height="400" viewBox="0 0 750 400">
                        <rect width="750" height="400" fill="#f8f9fa" rx="12"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">DARLIN.py Module Functions</text>

                        <!-- Function categories -->
                        <rect x="50" y="60" width="160" height="80" fill="#e74c3c" rx="10"/>
                        <text x="130" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Sequence Definitions</text>
                        <text x="130" y="100" text-anchor="middle" font-size="9" fill="white">• get_CARLIN_seq()</text>
                        <text x="130" y="115" text-anchor="middle" font-size="9" fill="white">• CA, TA, RA arrays</text>
                        <text x="130" y="130" text-anchor="middle" font-size="9" fill="white">• Template variants</text>

                        <rect x="230" y="60" width="160" height="80" fill="#27ae60" rx="10"/>
                        <text x="310" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Data Processing</text>
                        <text x="310" y="100" text-anchor="middle" font-size="9" fill="white">• extract_CARLIN_info()</text>
                        <text x="310" y="115" text-anchor="middle" font-size="9" fill="white">• Protocol handling</text>
                        <text x="310" y="130" text-anchor="middle" font-size="9" fill="white">• UMI processing</text>

                        <rect x="410" y="60" width="160" height="80" fill="#3498db" rx="10"/>
                        <text x="490" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Quality Control</text>
                        <text x="490" y="100" text-anchor="middle" font-size="9" fill="white">• Editing efficiency</text>
                        <text x="490" y="115" text-anchor="middle" font-size="9" fill="white">• Coverage metrics</text>
                        <text x="490" y="130" text-anchor="middle" font-size="9" fill="white">• Validation checks</text>

                        <rect x="590" y="60" width="160" height="80" fill="#9b59b6" rx="10"/>
                        <text x="670" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Integration</text>
                        <text x="670" y="100" text-anchor="middle" font-size="9" fill="white">• AnnData creation</text>
                        <text x="670" y="115" text-anchor="middle" font-size="9" fill="white">• Scanpy compatibility</text>
                        <text x="670" y="130" text-anchor="middle" font-size="9" fill="white">• Multi-omics support</text>

                        <!-- Key functions detail -->
                        <rect x="50" y="170" width="650" height="200" fill="white" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="375" y="195" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Key Functions in DARLIN.py</text>

                        <!-- Function 1 -->
                        <text x="80" y="220" font-size="12" font-weight="bold" fill="#e74c3c">get_CARLIN_seq(template, locus):</text>
                        <text x="100" y="235" font-size="10" fill="#333">Returns reference sequences for specified template and locus</text>
                        <text x="100" y="250" font-size="10" fill="#333">Supports: cCARLIN, Tigre_2022, Rosa variants</text>

                        <!-- Function 2 -->
                        <text x="80" y="270" font-size="12" font-weight="bold" fill="#27ae60">extract_CARLIN_info(data_path, SampleList):</text>
                        <text x="100" y="285" font-size="10" fill="#333">Extracts allele information from CARLIN analysis results</text>
                        <text x="100" y="300" font-size="10" fill="#333">Handles multiple samples and protocols</text>

                        <!-- Function 3 -->
                        <text x="80" y="320" font-size="12" font-weight="bold" fill="#3498db">generate_adata_from_CARLIN_result():</text>
                        <text x="100" y="335" font-size="10" fill="#333">Creates AnnData objects from CARLIN analysis</text>
                        <text x="100" y="350" font-size="10" fill="#333">Integrates with single-cell analysis workflows</text>

                        <!-- Protocol support -->
                        <rect x="100" y="380" width="550" height="15" fill="#d4edda" stroke="#27ae60" rx="3"/>
                        <text x="375" y="392" text-anchor="middle" font-size="11" font-weight="bold" fill="#27ae60">Supports: scCamellia, sc10xV3, BulkRNA protocols</text>
                    </svg>
                </div>

                <h3>larry.py - Sequence Quality Control Module</h3>
                <p>Named after the LARRY (Lineage Analysis by Reiterative Recombination in vivo) system, this module provides robust sequence processing and quality control:</p>

                <div class="algorithm-box">
                    <h4>Core QC Algorithm: UMI Clustering</h4>
                    <p>The module implements sophisticated UMI clustering to handle sequencing errors:</p>
                    <div class="function-signature">
def cluster_umis_by_edit_distance(umis, threshold=1):
    """
    Clusters UMIs based on edit distance to reduce sequencing errors

    Parameters:
    - umis: List of UMI sequences
    - threshold: Maximum edit distance for clustering

    Returns:
    - Clustered UMI groups with consensus sequences
    """
                    </div>
                </div>

                <h3>lineage.py - Downstream Analysis Module</h3>
                <p>Contains the core algorithms for lineage analysis and statistical computations:</p>

                <div class="module-box">
                    <span class="module-number">5</span>
                    <strong>Shannon Diversity Implementation:</strong> Calculates barcode diversity using information theory principles, accounting for both allele richness and evenness in the distribution.
                </div>

                <div class="algorithm-box">
                    <h4>Shannon Diversity Algorithm</h4>
                    $$H = -\sum_{i=1}^{S} p_i \log_2 p_i$$
                    $$\text{Shannon Diversity} = 2^H$$
                    <div class="function-signature">
def shannon_diversity(allele_counts):
    """
    Calculate Shannon diversity of allele distribution

    Parameters:
    - allele_counts: Array of allele frequencies

    Returns:
    - Shannon entropy (H) and Shannon diversity (2^H)
    """
    frequencies = allele_counts / np.sum(allele_counts)
    frequencies = frequencies[frequencies > 0]  # Remove zeros
    entropy = -np.sum(frequencies * np.log2(frequencies))
    diversity = 2 ** entropy
    return entropy, diversity
                    </div>
                </div>

                <h3>util.py - Mathematical Utilities</h3>
                <p>Provides essential mathematical and data manipulation functions:</p>

                <div class="module-box">
                    <span class="module-number">6</span>
                    <strong>Utility Functions:</strong> Includes functions for sequence alignment, statistical calculations, data format conversions, and mathematical operations commonly used in lineage analysis.
                </div>

                <div class="key-insight">
                    <strong>🔑 Module Design Pattern:</strong> Each module follows a consistent pattern with clear input/output specifications, comprehensive docstrings, and modular functions that can be used independently or as part of larger workflows.
                </div>
            </div>
        </div>

        <!-- Section 4: Key Algorithms -->
        <div class="section" id="algorithms">
            <div class="section-header">
                🧮 4. Key Algorithms and Mathematical Methods
            </div>
            <div class="section-content">
                <h3>Sequence Consensus Algorithm</h3>
                <p>MosaicLineage implements sophisticated consensus algorithms to handle sequencing errors and UMI clustering:</p>

                <div class="svg-container">
                    <svg width="750" height="400" viewBox="0 0 750 400">
                        <rect width="750" height="400" fill="#f8f9fa" rx="12"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">UMI Clustering and Consensus Algorithm</text>

                        <!-- Input UMIs with errors -->
                        <rect x="50" y="60" width="650" height="80" fill="#ffebee" stroke="#e74c3c" rx="10"/>
                        <text x="375" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Input: UMIs with Sequencing Errors</text>

                        <!-- UMI examples -->
                        <rect x="80" y="100" width="100" height="20" fill="#e74c3c" rx="3"/>
                        <text x="130" y="113" text-anchor="middle" font-size="9" fill="white">ATCGATCG</text>

                        <rect x="200" y="100" width="100" height="20" fill="#e74c3c" rx="3"/>
                        <text x="250" y="113" text-anchor="middle" font-size="9" fill="white">ATCGATCG</text>

                        <rect x="320" y="100" width="100" height="20" fill="#f39c12" rx="3"/>
                        <text x="370" y="113" text-anchor="middle" font-size="9" fill="white">ATCGATAG</text>

                        <rect x="440" y="100" width="100" height="20" fill="#e74c3c" rx="3"/>
                        <text x="490" y="113" text-anchor="middle" font-size="9" fill="white">ATCGATCG</text>

                        <rect x="560" y="100" width="100" height="20" fill="#f39c12" rx="3"/>
                        <text x="610" y="113" text-anchor="middle" font-size="9" fill="white">ATCGTTCG</text>

                        <!-- Clustering step -->
                        <rect x="50" y="160" width="650" height="80" fill="#e3f2fd" stroke="#3498db" rx="10"/>
                        <text x="375" y="180" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">Step 1: Edit Distance Clustering</text>

                        <!-- Clusters -->
                        <rect x="150" y="200" width="200" height="30" fill="#3498db" rx="5"/>
                        <text x="250" y="220" text-anchor="middle" font-size="10" fill="white">Cluster 1: ATCGATCG (3 copies)</text>

                        <rect x="400" y="200" width="200" height="30" fill="#3498db" rx="5"/>
                        <text x="500" y="220" text-anchor="middle" font-size="10" fill="white">Cluster 2: ATCGATAG, ATCGTTCG</text>

                        <!-- Consensus step -->
                        <rect x="50" y="260" width="650" height="80" fill="#e8f5e8" stroke="#27ae60" rx="10"/>
                        <text x="375" y="280" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Step 2: Consensus Generation</text>

                        <!-- Final consensus -->
                        <rect x="200" y="300" width="150" height="30" fill="#27ae60" rx="5"/>
                        <text x="275" y="320" text-anchor="middle" font-size="10" fill="white">ATCGATCG (Count: 3)</text>

                        <rect x="400" y="300" width="150" height="30" fill="#27ae60" rx="5"/>
                        <text x="475" y="320" text-anchor="middle" font-size="10" fill="white">ATCGATAG (Count: 2)</text>

                        <!-- Arrows -->
                        <path d="M 375 150 L 375 160" stroke="#666" stroke-width="3" marker-end="url(#arrowhead2)"/>
                        <path d="M 375 250 L 375 260" stroke="#666" stroke-width="3" marker-end="url(#arrowhead2)"/>

                        <!-- Algorithm parameters -->
                        <rect x="100" y="360" width="550" height="30" fill="#fff3cd" stroke="#f39c12" rx="5"/>
                        <text x="375" y="380" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Default Parameters: Edit Distance ≤ 1, Minimum Cluster Size = 2</text>

                        <defs>
                            <marker id="arrowhead2" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Clonal Coupling Algorithm</h3>
                <p>One of the most important algorithms in MosaicLineage calculates how frequently different cell types appear together within clones:</p>

                <div class="algorithm-box">
                    <h4>Clonal Coupling Score Calculation</h4>
                    $$\text{Coupling}(A,B) = \frac{\sum_{c} \min(f_{A,c}, f_{B,c})}{\sqrt{\sum_{c} f_{A,c}^2 \cdot \sum_{c} f_{B,c}^2}}$$
                    <p>Where:</p>
                    <ul>
                        <li>$f_{A,c}$ = fraction of cell type A in clone c</li>
                        <li>$f_{B,c}$ = fraction of cell type B in clone c</li>
                        <li>$c$ = clone index</li>
                    </ul>

                    <div class="function-signature">
def calculate_clonal_coupling(clone_matrix, cell_types):
    """
    Calculate pairwise clonal coupling scores

    Parameters:
    - clone_matrix: Cell type × Clone matrix
    - cell_types: List of cell type names

    Returns:
    - Coupling matrix (symmetric)
    """
    n_types = len(cell_types)
    coupling_matrix = np.zeros((n_types, n_types))

    for i in range(n_types):
        for j in range(n_types):
            if i != j:
                # Calculate coupling score
                numerator = np.sum(np.minimum(
                    clone_matrix[i, :], clone_matrix[j, :]
                ))
                denominator = np.sqrt(
                    np.sum(clone_matrix[i, :] ** 2) *
                    np.sum(clone_matrix[j, :] ** 2)
                )
                coupling_matrix[i, j] = numerator / denominator

    return coupling_matrix
                    </div>
                </div>

                <h3>Shared Clone Fraction Analysis</h3>
                <p>Critical for studying cell migration and circulation patterns:</p>

                <div class="algorithm-box">
                    <h4>Shared Clone Fraction Algorithm</h4>
                    $$\text{Shared Fraction} = \frac{|\text{Clones in Location A} \cap \text{Clones in Location B}|}{|\text{Clones in Location A}|}$$

                    <div class="function-signature">
def calculate_shared_clone_fraction(location_A_clones, location_B_clones):
    """
    Calculate fraction of clones shared between two locations

    Parameters:
    - location_A_clones: Set of clone IDs in location A
    - location_B_clones: Set of clone IDs in location B

    Returns:
    - Shared fraction (0 to 1)
    """
    shared_clones = set(location_A_clones) & set(location_B_clones)
    total_clones_A = len(set(location_A_clones))

    if total_clones_A > 0:
        return len(shared_clones) / total_clones_A
    else:
        return 0.0
                    </div>
                </div>

                <h3>Overlap Fraction Matrix Computation</h3>
                <p>Advanced algorithm for analyzing clone overlap patterns across multiple cell fates:</p>

                <div class="algorithm-flow">
                    <strong>Algorithm Steps:</strong>
                    <ol>
                        <li><strong>Reference Selection:</strong> Choose reference cell type/fate</li>
                        <li><strong>Clone Identification:</strong> Extract clones containing reference cells</li>
                        <li><strong>Overlap Calculation:</strong> Count clones shared with each other cell type</li>
                        <li><strong>Normalization:</strong> Calculate fraction relative to reference</li>
                    </ol>
                </div>

                <div class="key-insight">
                    <strong>💡 Algorithmic Innovation:</strong> The overlap fraction matrix algorithm enables systematic analysis of lineage relationships across multiple cell types simultaneously, revealing complex fate coupling patterns that would be difficult to detect with pairwise comparisons alone.
                </div>
            </div>
        </div>

        <!-- Section 5: Data Structures -->
        <div class="section" id="data-structures">
            <div class="section-header">
                📊 5. Data Structures and Formats
            </div>
            <div class="section-content">
                <h3>AnnData Integration for Single-Cell Analysis</h3>
                <p>MosaicLineage leverages the AnnData format for seamless integration with the single-cell analysis ecosystem:</p>

                <div class="svg-container">
                    <svg width="750" height="450" viewBox="0 0 750 450">
                        <rect width="750" height="450" fill="#f8f9fa" rx="12"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">AnnData Structure for DARLIN Analysis</text>

                        <!-- AnnData object -->
                        <rect x="250" y="60" width="250" height="120" fill="#3498db" stroke="#2980b9" stroke-width="3" rx="12"/>
                        <text x="375" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="white">AnnData Object</text>
                        <text x="375" y="105" text-anchor="middle" font-size="11" fill="white">adata</text>
                        <text x="375" y="120" text-anchor="middle" font-size="10" fill="white">Cells × Genes matrix</text>
                        <text x="375" y="135" text-anchor="middle" font-size="10" fill="white">+ Lineage information</text>
                        <text x="375" y="150" text-anchor="middle" font-size="10" fill="white">+ Metadata</text>
                        <text x="375" y="165" text-anchor="middle" font-size="10" fill="white">+ Embeddings</text>

                        <!-- Components -->
                        <!-- X matrix -->
                        <rect x="50" y="220" width="120" height="60" fill="#e74c3c" rx="8"/>
                        <text x="110" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">adata.X</text>
                        <text x="110" y="255" text-anchor="middle" font-size="9" fill="white">Gene expression</text>
                        <text x="110" y="270" text-anchor="middle" font-size="9" fill="white">Cells × Genes</text>

                        <!-- obs -->
                        <rect x="190" y="220" width="120" height="60" fill="#27ae60" rx="8"/>
                        <text x="250" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">adata.obs</text>
                        <text x="250" y="255" text-anchor="middle" font-size="9" fill="white">Cell metadata</text>
                        <text x="250" y="270" text-anchor="middle" font-size="9" fill="white">Clone IDs, types</text>

                        <!-- var -->
                        <rect x="330" y="220" width="120" height="60" fill="#f39c12" rx="8"/>
                        <text x="390" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">adata.var</text>
                        <text x="390" y="255" text-anchor="middle" font-size="9" fill="white">Gene metadata</text>
                        <text x="390" y="270" text-anchor="middle" font-size="9" fill="white">Gene names, IDs</text>

                        <!-- obsm -->
                        <rect x="470" y="220" width="120" height="60" fill="#9b59b6" rx="8"/>
                        <text x="530" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">adata.obsm</text>
                        <text x="530" y="255" text-anchor="middle" font-size="9" fill="white">Embeddings</text>
                        <text x="530" y="270" text-anchor="middle" font-size="9" fill="white">UMAP, PCA</text>

                        <!-- uns -->
                        <rect x="610" y="220" width="120" height="60" fill="#34495e" rx="8"/>
                        <text x="670" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="white">adata.uns</text>
                        <text x="670" y="255" text-anchor="middle" font-size="9" fill="white">Unstructured</text>
                        <text x="670" y="270" text-anchor="middle" font-size="9" fill="white">Parameters, plots</text>

                        <!-- Arrows from main object -->
                        <path d="M 300 180 L 150 220" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 350 180 L 250 220" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 375 180 L 390 220" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 425 180 L 530 220" stroke="#9b59b6" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 450 180 L 650 220" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead3)"/>

                        <!-- DARLIN-specific additions -->
                        <rect x="100" y="320" width="550" height="100" fill="white" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="375" y="340" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">DARLIN-Specific AnnData Extensions</text>

                        <text x="130" y="360" font-size="11" font-weight="bold" fill="#333">adata.obs columns:</text>
                        <text x="150" y="375" font-size="10" fill="#333">• 'clone_id': Unique clone identifier from 3 arrays</text>
                        <text x="150" y="390" font-size="10" fill="#333">• 'cell_type': Annotated cell type from expression</text>
                        <text x="150" y="405" font-size="10" fill="#333">• 'allele_CA', 'allele_TA', 'allele_RA': Individual array alleles</text>

                        <text x="450" y="360" font-size="11" font-weight="bold" fill="#333">adata.uns entries:</text>
                        <text x="470" y="375" font-size="10" fill="#333">• 'coupling_matrix': Clonal coupling scores</text>
                        <text x="470" y="390" font-size="10" fill="#333">• 'shannon_diversity': Barcode diversity metrics</text>
                        <text x="470" y="405" font-size="10" fill="#333">• 'allele_bank': Reference allele frequencies</text>

                        <defs>
                            <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>DataFrame Structures for Allele Data</h3>
                <p>The package uses standardized DataFrame formats for allele frequency and annotation data:</p>

                <div class="function-signature">
# Standard allele DataFrame structure
df_allele = pd.DataFrame({
    'allele': str,              # Allele sequence or ID
    'UMI_count': int,           # Frequency in dataset
    'sample_count': int,        # Number of samples containing allele
    'normalized_count': float,  # Frequency in reference bank
    'sample': str,              # Sample identifier
    'locus': str,              # Target locus (CA, TA, RA)
    'plate_ID': str,           # Experimental plate/batch
    'cell_type': str           # Associated cell type
})
                </div>

                <h3>Sparse Matrix Representations</h3>
                <p>For efficient memory usage with large datasets, MosaicLineage uses sparse matrices:</p>

                <div class="algorithm-box">
                    <h4>Clone-by-Cell Matrix Structure</h4>
                    <p>The core data structure for lineage analysis:</p>
                    $$\mathbf{X}_{clone} \in \mathbb{R}^{C \times N}$$
                    <p>Where:</p>
                    <ul>
                        <li>$C$ = number of cell types/fates</li>
                        <li>$N$ = number of unique clones</li>
                        <li>$X_{ij}$ = number of cells of type $i$ in clone $j$</li>
                    </ul>

                    <div class="function-signature">
# Example: coarse_X_clone matrix
coarse_X_clone = np.array([
    [10, 0, 5, 2],    # HSC counts per clone
    [0, 8, 3, 0],     # MPP counts per clone
    [2, 12, 0, 8],    # MyP counts per clone
    [0, 5, 15, 20]    # Mature counts per clone
])
# Shape: (4 cell types, 4 clones)
                    </div>
                </div>

                <div class="key-insight">
                    <strong>🔑 Data Structure Advantage:</strong> The sparse matrix representation allows efficient analysis of large-scale lineage datasets while maintaining compatibility with standard scientific Python tools like NumPy and SciPy.
                </div>
            </div>
        </div>

        <!-- Section 6: Analysis Pipeline -->
        <div class="section" id="analysis-pipeline">
            <div class="section-header">
                🔬 6. Analysis Pipeline Implementation
            </div>
            <div class="section-content">
                <h3>Complete Analysis Workflow</h3>
                <p>MosaicLineage implements a comprehensive analysis pipeline that processes raw DARLIN data through multiple stages:</p>

                <div class="svg-container">
                    <svg width="800" height="550" viewBox="0 0 800 550">
                        <rect width="800" height="550" fill="#f8f9fa" rx="15"/>

                        <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">MosaicLineage Analysis Pipeline</text>

                        <!-- Stage 1: Data Loading -->
                        <rect x="50" y="60" width="700" height="80" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Stage 1: Data Loading and Preprocessing</text>

                        <rect x="100" y="105" width="120" height="25" fill="#3498db" rx="5"/>
                        <text x="160" y="122" text-anchor="middle" font-size="9" fill="white">Load CARLIN results</text>

                        <rect x="240" y="105" width="120" height="25" fill="#3498db" rx="5"/>
                        <text x="300" y="122" text-anchor="middle" font-size="9" fill="white">Extract allele info</text>

                        <rect x="380" y="105" width="120" height="25" fill="#3498db" rx="5"/>
                        <text x="440" y="122" text-anchor="middle" font-size="9" fill="white">Protocol detection</text>

                        <rect x="520" y="105" width="120" height="25" fill="#3498db" rx="5"/>
                        <text x="580" y="122" text-anchor="middle" font-size="9" fill="white">Reference merging</text>

                        <!-- Stage 2: Quality Control -->
                        <rect x="50" y="160" width="700" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="12"/>
                        <text x="400" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Stage 2: Quality Control and Filtering</text>

                        <rect x="100" y="205" width="120" height="25" fill="#27ae60" rx="5"/>
                        <text x="160" y="222" text-anchor="middle" font-size="9" fill="white">UMI clustering</text>

                        <rect x="240" y="205" width="120" height="25" fill="#27ae60" rx="5"/>
                        <text x="300" y="222" text-anchor="middle" font-size="9" fill="white">Sequence consensus</text>

                        <rect x="380" y="205" width="120" height="25" fill="#27ae60" rx="5"/>
                        <text x="440" y="222" text-anchor="middle" font-size="9" fill="white">Rare allele filter</text>

                        <rect x="520" y="205" width="120" height="25" fill="#27ae60" rx="5"/>
                        <text x="580" y="222" text-anchor="middle" font-size="9" fill="white">Coverage thresholds</text>

                        <!-- Stage 3: Analysis -->
                        <rect x="50" y="260" width="700" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="3" rx="12"/>
                        <text x="400" y="285" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Stage 3: Statistical Analysis</text>

                        <rect x="100" y="305" width="120" height="25" fill="#f39c12" rx="5"/>
                        <text x="160" y="322" text-anchor="middle" font-size="9" fill="white">Shannon diversity</text>

                        <rect x="240" y="305" width="120" height="25" fill="#f39c12" rx="5"/>
                        <text x="300" y="322" text-anchor="middle" font-size="9" fill="white">Clonal coupling</text>

                        <rect x="380" y="305" width="120" height="25" fill="#f39c12" rx="5"/>
                        <text x="440" y="322" text-anchor="middle" font-size="9" fill="white">Shared fractions</text>

                        <rect x="520" y="305" width="120" height="25" fill="#f39c12" rx="5"/>
                        <text x="580" y="322" text-anchor="middle" font-size="9" fill="white">Migration analysis</text>

                        <!-- Stage 4: Visualization -->
                        <rect x="50" y="360" width="700" height="80" fill="#f3e5f5" stroke="#9b59b6" stroke-width="3" rx="12"/>
                        <text x="400" y="385" text-anchor="middle" font-size="16" font-weight="bold" fill="#9b59b6">Stage 4: Visualization and Reporting</text>

                        <rect x="150" y="405" width="120" height="25" fill="#9b59b6" rx="5"/>
                        <text x="210" y="422" text-anchor="middle" font-size="9" fill="white">Heatmaps</text>

                        <rect x="290" y="405" width="120" height="25" fill="#9b59b6" rx="5"/>
                        <text x="350" y="422" text-anchor="middle" font-size="9" fill="white">Distribution plots</text>

                        <rect x="430" y="405" width="120" height="25" fill="#9b59b6" rx="5"/>
                        <text x="490" y="422" text-anchor="middle" font-size="9" fill="white">Statistical reports</text>

                        <!-- Arrows between stages -->
                        <path d="M 400 150 L 400 160" stroke="#666" stroke-width="4" marker-end="url(#arrowhead4)"/>
                        <path d="M 400 250 L 400 260" stroke="#666" stroke-width="4" marker-end="url(#arrowhead4)"/>
                        <path d="M 400 350 L 400 360" stroke="#666" stroke-width="4" marker-end="url(#arrowhead4)"/>

                        <!-- Key functions -->
                        <rect x="100" y="470" width="600" height="60" fill="white" stroke="#34495e" stroke-width="2" rx="8"/>
                        <text x="400" y="490" text-anchor="middle" font-size="14" font-weight="bold" fill="#34495e">Key Pipeline Functions</text>
                        <text x="200" y="510" text-anchor="middle" font-size="10" fill="#333">generate_allele_info_across_experiments()</text>
                        <text x="500" y="510" text-anchor="middle" font-size="10" fill="#333">load_and_filter_CARLIN_data()</text>
                        <text x="350" y="525" text-anchor="middle" font-size="10" fill="#333">custom_conditional_heatmap()</text>

                        <defs>
                            <marker id="arrowhead4" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>Multi-Sample Integration Strategy</h3>
                <p>The pipeline implements sophisticated methods for combining data across multiple samples and experiments:</p>

                <div class="algorithm-box">
                    <h4>Cross-Experiment Allele Integration</h4>
                    <div class="function-signature">
def generate_allele_info_across_experiments(
    target_data_list,
    read_cutoff=3,
    root_path="/path/to/CARLIN/data",
    mouse_label="LL",
    sample_map=None,
    exclude_samples=[]
):
    """
    Integrates allele information across multiple experiments

    Key Steps:
    1. Load individual experiment results
    2. Merge with reference allele bank
    3. Apply quality filters
    4. Calculate cross-experiment statistics
    5. Generate singleton fraction analysis
    """
                    </div>
                </div>

                <h3>Quality Control Implementation</h3>
                <p>Comprehensive QC pipeline with multiple validation steps:</p>

                <div class="algorithm-flow">
                    <strong>QC Algorithm Flow:</strong>
                    <ol>
                        <li><strong>Read Coverage Filter:</strong> Apply minimum read cutoffs per UMI/cell barcode</li>
                        <li><strong>Allele Bank Validation:</strong> Cross-reference with reference allele frequencies</li>
                        <li><strong>Singleton Analysis:</strong> Calculate and validate singleton fractions</li>
                        <li><strong>Cross-Sample Consistency:</strong> Verify allele patterns across samples</li>
                    </ol>
                </div>

                <div class="algorithm-box">
                    <h4>Singleton Fraction Calculation</h4>
                    $$\text{Singleton Fraction} = \frac{\sum_{i} \mathbf{1}[\text{count}_i = 1]}{\text{Total Unique Alleles}}$$

                    <div class="function-signature">
# Implementation in analysis_script.py
singleton_fraction = np.sum(df_group["UMI_count"] == 1) / len(df_group)
single_fraction = np.sum(df_group["sample_count"] == 1) / len(df_group)
                    </div>
                </div>

                <div class="key-insight">
                    <strong>💡 Pipeline Innovation:</strong> The integration strategy automatically handles different experimental protocols and data formats, providing a unified analysis framework that can process both historical CARLIN data and new DARLIN experiments seamlessly.
                </div>
            </div>
        </div>

        <!-- Section 7: Visualization -->
        <div class="section" id="visualization">
            <div class="section-header">
                📊 7. Visualization and Plotting Framework
            </div>
            <div class="section-content">
                <h3>Plotting Module Architecture</h3>
                <p>MosaicLineage provides a comprehensive visualization framework with both standard and custom plotting functions:</p>

                <div class="svg-container">
                    <svg width="750" height="450" viewBox="0 0 750 450">
                        <rect width="750" height="450" fill="#f8f9fa" rx="12"/>

                        <text x="375" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Visualization Framework Architecture</text>

                        <!-- Core plotting functions -->
                        <rect x="50" y="60" width="650" height="100" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="12"/>
                        <text x="375" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Core Plotting Functions</text>

                        <rect x="80" y="105" width="130" height="45" fill="#27ae60" rx="8"/>
                        <text x="145" y="120" text-anchor="middle" font-size="10" fill="white">Heatmaps</text>
                        <text x="145" y="135" text-anchor="middle" font-size="9" fill="white">Coupling matrices</text>

                        <rect x="230" y="105" width="130" height="45" fill="#27ae60" rx="8"/>
                        <text x="295" y="120" text-anchor="middle" font-size="10" fill="white">Distributions</text>
                        <text x="295" y="135" text-anchor="middle" font-size="9" fill="white">Allele frequencies</text>

                        <rect x="380" y="105" width="130" height="45" fill="#27ae60" rx="8"/>
                        <text x="445" y="120" text-anchor="middle" font-size="10" fill="white">Scatter Plots</text>
                        <text x="445" y="135" text-anchor="middle" font-size="9" fill="white">Correlation analysis</text>

                        <rect x="530" y="105" width="130" height="45" fill="#27ae60" rx="8"/>
                        <text x="595" y="120" text-anchor="middle" font-size="10" fill="white">Time Series</text>
                        <text x="595" y="135" text-anchor="middle" font-size="9" fill="white">Temporal dynamics</text>

                        <!-- Specialized plots -->
                        <rect x="50" y="180" width="650" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="3" rx="12"/>
                        <text x="375" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">DARLIN-Specific Visualizations</text>

                        <rect x="100" y="225" width="140" height="25" fill="#f39c12" rx="5"/>
                        <text x="170" y="242" text-anchor="middle" font-size="9" fill="white">Three-array integration</text>

                        <rect x="260" y="225" width="140" height="25" fill="#f39c12" rx="5"/>
                        <text x="330" y="242" text-anchor="middle" font-size="9" fill="white">Insertion pattern plots</text>

                        <rect x="420" y="225" width="140" height="25" fill="#f39c12" rx="5"/>
                        <text x="490" y="242" text-anchor="middle" font-size="9" fill="white">Migration heatmaps</text>

                        <!-- Integration layer -->
                        <rect x="50" y="280" width="650" height="60" fill="#e3f2fd" stroke="#3498db" stroke-width="3" rx="12"/>
                        <text x="375" y="305" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">External Integration</text>

                        <rect x="150" y="320" width="120" height="25" fill="#3498db" rx="5"/>
                        <text x="210" y="337" text-anchor="middle" font-size="9" fill="white">Matplotlib backend</text>

                        <rect x="290" y="320" width="120" height="25" fill="#3498db" rx="5"/>
                        <text x="350" y="337" text-anchor="middle" font-size="9" fill="white">Seaborn styling</text>

                        <rect x="430" y="320" width="120" height="25" fill="#3498db" rx="5"/>
                        <text x="490" y="337" text-anchor="middle" font-size="9" fill="white">Scanpy integration</text>

                        <!-- Key plotting functions -->
                        <rect x="100" y="370" width="550" height="60" fill="white" stroke="#bdc3c7" stroke-width="2" rx="8"/>
                        <text x="375" y="390" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Plotting Functions</text>
                        <text x="200" y="410" text-anchor="middle" font-size="10" fill="#333">custom_conditional_heatmap()</text>
                        <text x="400" y="410" text-anchor="middle" font-size="10" fill="#333">plot_allele_frequency_distribution()</text>
                        <text x="600" y="410" text-anchor="middle" font-size="10" fill="#333">plot_coupling_matrix()</text>
                    </svg>
                </div>

                <h3>Custom Heatmap Implementation</h3>
                <p>One of the most sophisticated visualization functions in the package:</p>

                <div class="algorithm-box">
                    <h4>Conditional Heatmap Algorithm</h4>
                    <div class="function-signature">
def custom_conditional_heatmap(
    df_input,
    x_name='x_axis',
    y_name='y_axis',
    value_name='value',
    normalize_row=True,
    normalize_col=False,
    log_transform=False,
    **kwargs
):
    """
    Creates conditional heatmaps with flexible normalization

    Features:
    - Row/column normalization options
    - Log transformation for skewed data
    - Custom color schemes
    - Statistical annotations
    """
                    </div>
                </div>

                <h3>Statistical Plotting Functions</h3>
                <p>The package includes specialized functions for lineage-specific statistical visualizations:</p>

                <table class="architecture-table">
                    <thead>
                        <tr>
                            <th>Function</th>
                            <th>Purpose</th>
                            <th>Key Features</th>
                            <th>Output Format</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>plot_allele_frequency_distribution()</strong></td>
                            <td>Visualize allele frequency patterns</td>
                            <td>Log-scale, cumulative plots</td>
                            <td>PNG, PDF</td>
                        </tr>
                        <tr>
                            <td><strong>plot_coupling_matrix()</strong></td>
                            <td>Display clonal coupling relationships</td>
                            <td>Hierarchical clustering, annotations</td>
                            <td>Heatmap with dendrograms</td>
                        </tr>
                        <tr>
                            <td><strong>plot_shared_clone_analysis()</strong></td>
                            <td>Migration pattern visualization</td>
                            <td>Multi-tissue comparisons</td>
                            <td>Bar plots, scatter plots</td>
                        </tr>
                        <tr>
                            <td><strong>plot_insertion_deletion_patterns()</strong></td>
                            <td>TdT signature analysis</td>
                            <td>Cumulative distributions</td>
                            <td>Multi-panel figures</td>
                        </tr>
                    </tbody>
                </table>

                <h3>Integration with Scientific Python Ecosystem</h3>
                <div class="algorithm-flow">
                    <strong>Visualization Stack Integration:</strong>
                    <ul>
                        <li><strong>Matplotlib:</strong> Core plotting backend with publication-quality output</li>
                        <li><strong>Seaborn:</strong> Statistical visualization and aesthetic styling</li>
                        <li><strong>Scanpy:</strong> Single-cell specific plots (UMAP, violin plots)</li>
                        <li><strong>Plotly:</strong> Interactive visualizations for exploration</li>
                    </ul>
                </div>

                <h3>Simulation and Modeling Framework</h3>
                <p>The simulate.py module provides tools for generating synthetic lineage data:</p>

                <div class="algorithm-box">
                    <h4>Power Law Simulation Model</h4>
                    $$P(\text{clone size} = k) \propto k^{-\alpha}$$
                    <p>Where $\alpha$ is the power law exponent controlling clone size distribution</p>

                    <div class="function-signature">
def simulate_power_law_clones(n_cells, alpha=2.0, min_clone_size=1):
    """
    Generate synthetic clone size distribution following power law

    Parameters:
    - n_cells: Total number of cells to simulate
    - alpha: Power law exponent
    - min_clone_size: Minimum clone size

    Returns:
    - Array of clone sizes following power law distribution
    """
                    </div>
                </div>

                <div class="key-insight">
                    <strong>🎨 Visualization Philosophy:</strong> MosaicLineage emphasizes publication-ready visualizations with consistent styling, comprehensive legends, and statistical annotations. All plots are designed to be both informative for analysis and suitable for scientific publication.
                </div>

                <h3>Example Usage Workflow</h3>
                <div class="code-block">
# Complete MosaicLineage analysis workflow
import mosaiclineage.help_functions as ml

# 1. Load and process DARLIN data
adata = ml.generate_adata_from_CARLIN_result(
    data_path="path/to/CARLIN/results",
    sample_list=["HSC", "MPP", "MyP"],
    protocol="sc10xV3"
)

# 2. Calculate diversity metrics
shannon_div = ml.calculate_shannon_diversity(adata.obs['clone_id'])

# 3. Compute clonal coupling
coupling_matrix = ml.calculate_clonal_coupling(
    adata.obs['clone_id'],
    adata.obs['cell_type']
)

# 4. Generate visualizations
ml.plot_coupling_matrix(coupling_matrix, save_path="coupling.png")
ml.custom_conditional_heatmap(
    df_allele,
    x_name='cell_type',
    y_name='clone_id',
    value_name='frequency'
)

# 5. Export results
adata.write("darlin_analysis.h5ad")
                </div>

                <div class="algorithm-flow">
                    <strong>🚀 Performance Features:</strong>
                    <ul>
                        <li><strong>Vectorized Operations:</strong> NumPy-based calculations for speed</li>
                        <li><strong>Memory Efficiency:</strong> Sparse matrix support for large datasets</li>
                        <li><strong>Parallel Processing:</strong> Multi-core support for intensive computations</li>
                        <li><strong>Caching:</strong> Intermediate result caching to avoid recomputation</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding: 50px; color: white; background: rgba(0,0,0,0.1); border-radius: 20px; margin-top: 30px;">
            <p style="font-size: 1.3em; font-weight: bold;">MosaicLineage Code Structure Tutorial</p>
            <p style="margin-top: 10px;">Comprehensive Guide to Advanced Lineage Tracing Analysis Framework</p>
            <p style="margin-top: 15px; font-size: 0.95em;">Based on: ShouWenWang-Lab/MosaicLineage GitHub Repository</p>
            <p style="margin-top: 8px; font-size: 0.95em;">Supporting Li et al. (2023) Cell - DARLIN Project</p>
        </div>
    </div>
</body>
</html>
